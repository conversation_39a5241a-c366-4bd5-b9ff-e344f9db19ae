--
-- PostgreSQL database dump
--

-- Dumped from database version 10.23 (Ubuntu 10.23-1.pgdg18.04+1)
-- Dumped by pg_dump version 10.23 (Ubuntu 10.23-1.pgdg18.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: hist_cluster; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA hist_cluster;


ALTER SCHEMA hist_cluster OWNER TO postgres;

--
-- Name: pglogical; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA pglogical;


ALTER SCHEMA pglogical OWNER TO postgres;

--
-- Name: swadaptergos; Type: SCHEMA; Schema: -; Owner: swadaptergos
--

CREATE SCHEMA swadaptergos;


ALTER SCHEMA swadaptergos OWNER TO swadaptergos;

--
-- Name: swadapterqs; Type: SCHEMA; Schema: -; Owner: swadapterqs
--

CREATE SCHEMA swadapterqs;


ALTER SCHEMA swadapterqs OWNER TO swadapterqs;

--
-- Name: swgameserver; Type: SCHEMA; Schema: -; Owner: swgameserver
--

CREATE SCHEMA swgameserver;


ALTER SCHEMA swgameserver OWNER TO swgameserver;

--
-- Name: swjackpot; Type: SCHEMA; Schema: -; Owner: swjackpot
--

CREATE SCHEMA swjackpot;


ALTER SCHEMA swjackpot OWNER TO swjackpot;

--
-- Name: swmanagement; Type: SCHEMA; Schema: -; Owner: swmanagement
--

CREATE SCHEMA swmanagement;


ALTER SCHEMA swmanagement OWNER TO swmanagement;

--
-- Name: swsrt; Type: SCHEMA; Schema: -; Owner: swsrt
--

CREATE SCHEMA swsrt;


ALTER SCHEMA swsrt OWNER TO swsrt;

--
-- Name: swsystem; Type: SCHEMA; Schema: -; Owner: swsystem
--

CREATE SCHEMA swsystem;


ALTER SCHEMA swsystem OWNER TO swsystem;

--
-- Name: plpgsql; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION plpgsql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION plpgsql IS 'PL/pgSQL procedural language';


--
-- Name: dblink; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS dblink WITH SCHEMA public;


--
-- Name: EXTENSION dblink; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION dblink IS 'connect to other PostgreSQL databases from within a database';


--
-- Name: pg_hashids; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS pg_hashids WITH SCHEMA public;


--
-- Name: EXTENSION pg_hashids; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_hashids IS 'pg_hashids';


--
-- Name: pg_pathman; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS pg_pathman WITH SCHEMA public;


--
-- Name: EXTENSION pg_pathman; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_pathman IS 'Partitioning tool for PostgreSQL';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_stat_statements IS 'track execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: postgres_fdw; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS postgres_fdw WITH SCHEMA public;


--
-- Name: EXTENSION postgres_fdw; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION postgres_fdw IS 'foreign-data wrapper for remote PostgreSQL servers';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: enum_jackpot_operation_status; Type: TYPE; Schema: swjackpot; Owner: swsystem
--

CREATE TYPE swjackpot.enum_jackpot_operation_status AS ENUM (
    'pending',
    'resolved',
    'rejected'
);


ALTER TYPE swjackpot.enum_jackpot_operation_status OWNER TO swsystem;

--
-- Name: enum_audits_initiator_type; Type: TYPE; Schema: swmanagement; Owner: swmanagement
--

CREATE TYPE swmanagement.enum_audits_initiator_type AS ENUM (
    'user',
    'player',
    'system'
);


ALTER TYPE swmanagement.enum_audits_initiator_type OWNER TO swmanagement;

--
-- Name: enum_recovery_type; Type: TYPE; Schema: swmanagement; Owner: swmanagement
--

CREATE TYPE swmanagement.enum_recovery_type AS ENUM (
    'force-finish',
    'revert',
    'finalize'
);


ALTER TYPE swmanagement.enum_recovery_type OWNER TO swmanagement;

--
-- Name: enum_sessions_history_interruption_reason; Type: TYPE; Schema: swmanagement; Owner: swmanagement
--

CREATE TYPE swmanagement.enum_sessions_history_interruption_reason AS ENUM (
    'session_expired',
    'pending_payment',
    'pending_jackpot',
    'broken_game_context'
);


ALTER TYPE swmanagement.enum_sessions_history_interruption_reason OWNER TO swmanagement;

--
-- Name: enum_wallet_entity_payment_log_transfer_type; Type: TYPE; Schema: swmanagement; Owner: swmanagement
--

CREATE TYPE swmanagement.enum_wallet_entity_payment_log_transfer_type AS ENUM (
    'ent-ent',
    'ent-plr',
    'plr-ent',
    'ent-plr_ext',
    'plr-ent_ext',
    'free-rebet',
    'bns-redeem',
    'free-bet'
);


ALTER TYPE swmanagement.enum_wallet_entity_payment_log_transfer_type OWNER TO swmanagement;

--
-- Name: enum_wallet_win_bet_ggr_calculation; Type: TYPE; Schema: swmanagement; Owner: swsystem
--

CREATE TYPE swmanagement.enum_wallet_win_bet_ggr_calculation AS ENUM (
    'round',
    'wallet'
);


ALTER TYPE swmanagement.enum_wallet_win_bet_ggr_calculation OWNER TO swsystem;

--
-- Name: enum_wallet_win_bet_transaction_type; Type: TYPE; Schema: swmanagement; Owner: swmanagement
--

CREATE TYPE swmanagement.enum_wallet_win_bet_transaction_type AS ENUM (
    'free_bet',
    'jackpot',
    'free_rebet',
    'redeem',
    'transfer',
    'bonus'
);


ALTER TYPE swmanagement.enum_wallet_win_bet_transaction_type OWNER TO swmanagement;

--
-- Name: fnc_hist_run_background_jobs(); Type: FUNCTION; Schema: hist_cluster; Owner: swsystem
--

CREATE FUNCTION hist_cluster.fnc_hist_run_background_jobs() RETURNS void
    LANGUAGE plpgsql
    SET client_min_messages TO 'log'
    AS $_$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:
   Purpose    :
   History    :
      1.0.0
         Date    : Jan 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release

   Sample run:
      SELECT hist_cluster.fnc_hist_run_background_jobs();

   PG Cron:
      SELECT cron.schedule('* * * * *', $$SELECT hist_cluster.fnc_hist_run_background_jobs()$$);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777775555555;
   rec_jobs    hist_cluster.hist_jobs%ROWTYPE;
BEGIN
-- Check if MDB
   IF (SELECT pg_is_in_recovery()) THEN
      RAISE LOG 'HIST-ETL: Not a Master DB. Skip and exit now'; RETURN;
   END IF;

-- Singleton
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RAISE LOG 'HIST-ETL: Can''t get exclusive lock. Exit now'; RETURN;
   END IF;

   -- Fetch JOB to process
   BEGIN
      SELECT *
      FROM   hist_cluster.hist_jobs
      WHERE  completed_at IS NULL
      ORDER  BY job_id
      LIMIT 1
      INTO   STRICT rec_jobs;
   EXCEPTION
      WHEN no_data_found THEN
         RAISE LOG 'HIST-ETL: No any jobs to process. Exit now'; RETURN;
      WHEN others THEN
         RAISE EXCEPTION 'HIST-ETL: Unexpected error during JOB load: %', SQLERRM;
   END;

   -- Check there is no active analyze on this table
   IF EXISTS (SELECT NULL FROM pg_stat_activity WHERE query ~* 'analyze' AND query ~* (rec_jobs.schema_name||'.'||rec_jobs.table_name) AND pid != pg_backend_pid()) THEN
      RAISE LOG 'HIST-ETL: Active system analyze of %.% is in progress. Skip', rec_jobs.schema_name, rec_jobs.table_name; RETURN;
   END IF;

   -- Do job
   IF rec_jobs.action = 'analyze' THEN
      IF (SELECT last_autoanalyze IS NULL from pg_stat_all_tables where schemaname = rec_jobs.schema_name and relname = rec_jobs.table_name) THEN
         EXECUTE FORMAT('ANALYZE "%s"."%s"', rec_jobs.schema_name, rec_jobs.table_name);
      ELSE
         RAISE LOG 'HIST-ETL: Table %.% is already analyzed', rec_jobs.schema_name, rec_jobs.table_name;
      END IF;

   ELSIF rec_jobs.action = 'reindex' THEN
      EXECUTE FORMAT($sql1$ SELECT public.fnc_transfer_finish_table(p_schema_name:='%s', p_table_name:= '%s') $sql1$, rec_jobs.schema_name, rec_jobs.table_name);
   ELSE
      RAISE EXCEPTION 'HIST-ETL: Unknown JOB action: "%"', rec_jobs.action;
   END IF;

   UPDATE hist_cluster.hist_jobs SET
          started_at    = current_timestamp AT TIME ZONE 'UTC'
         ,completed_at  = clock_timestamp() AT TIME ZONE 'UTC'
   WHERE  job_id = rec_jobs.job_id;

   RAISE LOG 'HIST-ETL: Completed % for %.%', rec_jobs.action, rec_jobs.schema_name, rec_jobs.table_name; RETURN;
END;
$_$;


ALTER FUNCTION hist_cluster.fnc_hist_run_background_jobs() OWNER TO swsystem;

--
-- Name: fnc_transfer_finish_table(character varying, character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_finish_table
   Purpose    :   Reindex disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Reindex disabled indexes only (due to always enabled PK index)

   Sample run:
      SELECT * FROM public.fnc_transfer_finish_table (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777777777777;
   rec_idx     RECORD;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   -- Get all table indexes
   FOR rec_idx IN SELECT i.indexrelid, i.indisready, i.indisvalid, i.indisprimary, i.indrelid
                        ,si.nspname AS idx_schema, ci.relname AS idx_name
                  FROM   pg_index i
                         INNER JOIN pg_class ct ON i.indrelid = ct.oid
                         INNER JOIN pg_class ci ON i.indexrelid = ci.oid
                         INNER JOIN pg_namespace st ON ct.relnamespace = st.oid
                         INNER JOIN pg_namespace si ON ci.relnamespace = si.oid
                  WHERE  ct.relname = p_table_name
                    AND  st.nspname = p_schema_name
                    AND NOT (i.indisready AND i.indisvalid)
   LOOP
      -- Enable table indexes
      UPDATE pg_index u SET
             indisready = TRUE
            ,indisvalid = TRUE
      WHERE u.indexrelid = rec_idx.indexrelid;

      EXECUTE FORMAT('REINDEX INDEX %s.%s',
                        rec_idx.idx_schema,
                        rec_idx.idx_name
                    );
   END LOOP;

   RETURN 'OK';
END
$$;


ALTER FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) OWNER TO postgres;

--
-- Name: fnc_transfer_get_random_tblspcs(integer, character varying); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) RETURNS text[]
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_get_random_tblspcs
   Purpose    :   To get array of tablespaces ordered by random, but take its size into account
   History    :
      1.0.0
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Initial release

      1.0.1
         Date    : Jul 04, 2019
         Authors : Timur Luchkin
         Notes   : Fix division by zero

   Sample run:
      SELECT public.fnc_transfer_get_random_tblspcs (p_number := 10, p_exclude_tblspc := NULL);

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
BEGIN
   RETURN
   (
      WITH cte_source AS (
                        SELECT tablespace
                              --,disk_size_pretty
                              ,size_percent
                              ,distance
                              ,CASE WHEN distance <= 15 THEN distance ^ 3.0
                                                  ELSE 5000
                               END::INTEGER AS distance_power
                        FROM   (
                                 SELECT tablespace
                                       --,disk_size_pretty
                                       ,size_percent
                                       ,Abs(Min(1 - size_percent) OVER () - (1 - size_percent)) * 100 + 4  AS distance
                                 FROM  (
                                          SELECT tablespace
                                                ,disk_size
                                                --,pg_size_pretty(disk_size) AS disk_size_pretty
                                                -- ,Round(disk_size / Sum(disk_size) OVER (), 2) AS size_percent
                                                ,Round(CASE WHEN (Sum(disk_size) OVER ()) = 0 THEN 0 ELSE disk_size / (Sum(disk_size) OVER ()) END, 2) AS size_percent
                                          FROM  (
                                                   SELECT ts.spcname AS tablespace
                                                         ,pg_tablespace_size(ts.spcname) AS disk_size
                                                   FROM   pg_tablespace ts
                                                          INNER JOIN
                                                          pathman_allowed_tspaces alts ON ts.spcname = alts.ts_name
                                                   --WHERE  ts.spcname != p_exclude_tblspc
                                                   WHERE  ts.spcname != Coalesce(p_exclude_tblspc, 'N/A')
                                                ) t
                                       ) t2
                              ) t3
                     )
      SELECT array_agg(tbs_array order by random())
      FROM   (
               SELECT *
               FROM   (
                        SELECT unnest(array_remove (string_to_array (repeat(tablespace||'|', distance_power), '|'), '')) AS tbs_array
                        FROM   cte_source
                      ) t99
               ORDER BY random()
               LIMIT p_number
             ) t100
   );
END
$$;


ALTER FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) OWNER TO swsystem;

--
-- Name: fnc_transfer_prepare_table(character varying, character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_transfer_prepare_table
   Purpose    :   To randomize tablespaces for table and its indexes and disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Primary key index will be not disabled to support logical replication

     1.0.2
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Add a smarter way to choose random tablespaces (see function fnc_transfer_get_random_tblspcs)
                   Do not allow to change tablespaces for non-empty table

   Sample run:
      SELECT * FROM public.fnc_transfer_prepare_table (p_schema_name := 'swmanagement', p_table_name := 'audits_72');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id         BIGINT := 7777777777777777;
   rec1              RECORD;
   ts_name_all       TEXT := '';
   records_count     BIGINT;
   obj_count         INTEGER;
   arr_tablespaces   TEXT[];
   i                 INTEGER;
   err_msg           TEXT;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   EXECUTE FORMAT('SELECT Count(*) FROM "%s"."%s"', p_schema_name, p_table_name) INTO records_count;
   IF records_count > 0 THEN
      RETURN 'Table is not empty. Exit';
   END IF;

   -- Get total number of objects to split among tablespaces
   EXECUTE FORMAT ('SELECT count(*) FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ', p_schema_name, p_table_name) INTO obj_count;
   obj_count := obj_count + 1; -- table itself

   BEGIN
      arr_tablespaces := public.fnc_transfer_get_random_tblspcs (p_number := obj_count, p_exclude_tblspc := NULL::VARCHAR);

      IF array_length(arr_tablespaces, 1) = obj_count THEN
         i := 1;
      ELSE
         i := -1;
      END IF;
   EXCEPTION
      WHEN others THEN
         i := -1;
         err_msg := SQLERRM;
   END;

   -- set random tablespace from the list of allowed
   IF i = 1 THEN
      -- move partition to the tablespace
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" SET TABLESPACE %s',
         p_schema_name,
         p_table_name,
         arr_tablespaces[i]);

      ts_name_all := arr_tablespaces[i];
      i := i + 1;

      -- move indexes
      FOR rec1 IN EXECUTE FORMAT ('SELECT schemaname, indexname FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ',
                                  p_schema_name,
                                  p_table_name
                                 )
      LOOP

         EXECUTE FORMAT('ALTER INDEX %s.%s SET TABLESPACE %s',
            rec1.schemaname,
            rec1.indexname,
            arr_tablespaces[i]);

         ts_name_all := ts_name_all ||'; '|| arr_tablespaces[i];
         i := i + 1;
      END LOOP;
   END IF;

   -- Disable table indexes
   UPDATE pg_index SET
          indisready = FALSE
         ,indisvalid = FALSE
   WHERE  indrelid = (
                      SELECT oid
                      FROM   pg_class
                      WHERE  relname = p_table_name
                        AND  relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = p_schema_name)
                     )
     AND NOT indisprimary
   ;

   IF i = -1 THEN
      RETURN FORMAT ('Tablespace part has been failed: %s', err_msg);
   ELSE
      RETURN ts_name_all;
   END IF;
END
$$;


ALTER FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) OWNER TO postgres;

--
-- Name: fnc_zfs_change_storage(character varying, character varying); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   fnc_zfs_change_storage
   Purpose    :   Change storage type from EXTENDED to EXTERNAL for all columns
                  We do not need additional compression in PG on ZFS
   History    :
      1.0.0
         Date    : Nov 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release


   Sample run:
      SELECT * FROM public.fnc_zfs_change_storage (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec_loop       RECORD;
   v_lock_id      BIGINT := 7777777777777777;
BEGIN
   -- Lets lock the logic to prevent a mess
   IF NOT pg_try_advisory_xact_lock(v_lock_id) THEN
      RETURN 'ALREADY_RUN';
   END IF;

   FOR rec_loop IN   SELECT ns.nspname, tbl.relname, att.attname
                     FROM   pg_attribute att
                            INNER JOIN pg_class tbl ON tbl.oid = att.attrelid
                            INNER JOIN pg_namespace ns ON tbl.relnamespace = ns.oid
                     WHERE tbl.relname = p_table_name
                       AND ns.nspname = p_schema_name
                       AND att.attstorage = 'x' -- EXTENDED
                       AND NOT att.attisdropped
   LOOP
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" ALTER "%s" SET STORAGE EXTERNAL'
                     ,rec_loop.nspname
                     ,rec_loop.relname
                     ,rec_loop.attname
                    );

      SET client_min_messages TO INFO;
      RAISE INFO 'Change storage for %', rec_loop.attname;
      RESET client_min_messages;
   END LOOP;

   RETURN 'OK';
END
$$;


ALTER FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

--
-- Name: special_fnc_transfer_finish_table(character varying, character varying); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_transfer_finish_table
   Purpose    :   Reindex disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Reindex disabled indexes only (due to always enabled PK index)

   Sample run:
      SELECT * FROM public.special_fnc_transfer_finish_table (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id   BIGINT := 7777777777777777;
   rec_idx     RECORD;
BEGIN
   -- Get all table indexes
   -- FOR rec_idx IN SELECT i.indexrelid, i.indisready, i.indisvalid, i.indisprimary, i.indrelid
   --                      ,si.nspname AS idx_schema, ci.relname AS idx_name
   --                FROM   pg_index i
   --                       INNER JOIN pg_class ct ON i.indrelid = ct.oid
   --                       INNER JOIN pg_class ci ON i.indexrelid = ci.oid
   --                       INNER JOIN pg_namespace st ON ct.relnamespace = st.oid
   --                       INNER JOIN pg_namespace si ON ci.relnamespace = si.oid
   --                WHERE  ct.relname = p_table_name
   --                  AND  st.nspname = p_schema_name
   --                  AND NOT (i.indisready AND i.indisvalid)
   -- LOOP
   --    -- Enable table indexes
   --    UPDATE pg_index u SET
   --           indisready = TRUE
   --          ,indisvalid = TRUE
   --    WHERE u.indexrelid = rec_idx.indexrelid;
   --
   -- END LOOP;

   EXECUTE FORMAT('REINDEX TABLE %s.%s',
                     p_schema_name,
                     p_schema_name
               );



   RETURN 'OK';
END
$$;


ALTER FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

--
-- Name: special_fnc_transfer_prepare_table(character varying, character varying); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_transfer_prepare_table
   Purpose    :   To randomize tablespaces for table and its indexes and disable indexes
   History    :
      1.0.0
         Date    : Oct 01, 2018
         Authors : Timur Luchkin
         Notes   : Initial release

     1.0.1
         Date    : Jan 07, 2019
         Authors : Timur Luchkin
         Notes   : Primary key index will be not disabled to support logical replication

     1.0.2
         Date    : Jan 25, 2019
         Authors : Timur Luchkin
         Notes   : Add a smarter way to choose random tablespaces (see function fnc_transfer_get_random_tblspcs)
                   Do not allow to change tablespaces for non-empty table

   Sample run:
      SELECT * FROM public.special_fnc_transfer_prepare_table (p_schema_name := 'swmanagement', p_table_name := 'audits_72');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   v_lock_id         BIGINT := 7777777777777777;
   rec1              RECORD;
   ts_name_all       TEXT := '';
   records_count     BIGINT;
   obj_count         INTEGER;
   arr_tablespaces   TEXT[];
   i                 INTEGER;
   err_msg           TEXT;
BEGIN

   -- TLU: TODO: Optimize next check with just LIMIT 1 - NOT FOUND
   EXECUTE FORMAT('SELECT Count(*) FROM "%s"."%s"', p_schema_name, p_table_name) INTO records_count;
   IF records_count > 0 THEN
      RETURN 'Table is not empty. Exit';
   END IF;

   -- Get total number of objects to split among tablespaces
   EXECUTE FORMAT ('SELECT count(*) FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ', p_schema_name, p_table_name) INTO obj_count;
   obj_count := obj_count + 1; -- table itself

   BEGIN
      arr_tablespaces := public.fnc_transfer_get_random_tblspcs (p_number := obj_count, p_exclude_tblspc := NULL::VARCHAR);

      IF array_length(arr_tablespaces, 1) = obj_count THEN
         i := 1;
      ELSE
         i := -1;
      END IF;
   EXCEPTION
      WHEN others THEN
         i := -1;
         err_msg := SQLERRM;
   END;

   -- set random tablespace from the list of allowed
   IF i = 1 THEN
      -- move partition to the tablespace
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" SET TABLESPACE %s',
         p_schema_name,
         p_table_name,
         arr_tablespaces[i]);

      ts_name_all := arr_tablespaces[i];
      i := i + 1;

      -- move indexes
      FOR rec1 IN EXECUTE FORMAT ('SELECT schemaname, indexname FROM pg_indexes WHERE schemaname = ''%s'' and tablename = ''%s'' ',
                                  p_schema_name,
                                  p_table_name
                                 )
      LOOP

         EXECUTE FORMAT('ALTER INDEX %s.%s SET TABLESPACE %s',
            rec1.schemaname,
            rec1.indexname,
            arr_tablespaces[i]);

         ts_name_all := ts_name_all ||'; '|| arr_tablespaces[i];
         i := i + 1;
      END LOOP;
   END IF;

   -- Disable table indexes
   UPDATE pg_index SET
          indisready = FALSE
         ,indisvalid = FALSE
   WHERE  indrelid = (
                      SELECT oid
                      FROM   pg_class
                      WHERE  relname = p_table_name
                        AND  relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = p_schema_name)
                     )
   ;

   IF i = -1 THEN
      RETURN FORMAT ('Tablespace part has been failed: %s', err_msg);
   ELSE
      RETURN ts_name_all;
   END IF;
END
$$;


ALTER FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

--
-- Name: special_fnc_zfs_change_storage(character varying, character varying); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO 'public'
    AS $$
/*
~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ > ~ >

   Object Name:   special_fnc_zfs_change_storage
   Purpose    :   Change storage type from EXTENDED to EXTERNAL for all columns
                  We do not need additional compression in PG on ZFS
   History    :
      1.0.0
         Date    : Nov 11, 2019
         Authors : Timur Luchkin
         Notes   : Initial release


   Sample run:
      SELECT * FROM public.special_fnc_zfs_change_storage (p_schema_name := 'swmanagement', p_table_name := 'rounds_history_30');

~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ < ~ <
*/
DECLARE
   rec_loop       RECORD;
   v_lock_id      BIGINT := 7777777777777777;
BEGIN

   FOR rec_loop IN   SELECT ns.nspname, tbl.relname, att.attname
                     FROM   pg_attribute att
                            INNER JOIN pg_class tbl ON tbl.oid = att.attrelid
                            INNER JOIN pg_namespace ns ON tbl.relnamespace = ns.oid
                     WHERE tbl.relname = p_table_name
                       AND ns.nspname = p_schema_name
                       AND att.attstorage = 'x' -- EXTENDED
                       AND NOT att.attisdropped
   LOOP
      EXECUTE FORMAT('ALTER TABLE "%s"."%s" ALTER "%s" SET STORAGE EXTERNAL'
                     ,rec_loop.nspname
                     ,rec_loop.relname
                     ,rec_loop.attname
                    );

      SET client_min_messages TO INFO;
      RAISE INFO 'Change storage for %', rec_loop.attname;
      RESET client_min_messages;
   END LOOP;

   RETURN 'OK';
END
$$;


ALTER FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) OWNER TO swsystem;

--
-- Name: sw_get_internal_id(text); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.sw_get_internal_id(public_id text, OUT internal_id bigint) RETURNS bigint
    LANGUAGE plpgsql STABLE STRICT SECURITY DEFINER
    AS $$
   BEGIN
      SELECT (public.id_decode(public_id, po_hash_salt, po_hash_length))[1]
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO internal_id;
   END;
   $$;


ALTER FUNCTION public.sw_get_internal_id(public_id text, OUT internal_id bigint) OWNER TO swsystem;

--
-- Name: sw_get_public_id(bigint); Type: FUNCTION; Schema: public; Owner: swsystem
--

CREATE FUNCTION public.sw_get_public_id(internal_id bigint, OUT public_id text) RETURNS text
    LANGUAGE plpgsql STABLE STRICT SECURITY DEFINER
    AS $$
   BEGIN
      SELECT public.id_encode(internal_id, po_hash_salt, po_hash_length)
      FROM   swsystem.get_sw_hashid ('sw-falcon') INTO public_id;
   END;
   $$;


ALTER FUNCTION public.sw_get_public_id(internal_id bigint, OUT public_id text) OWNER TO swsystem;

--
-- Name: test_acid(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.test_acid() RETURNS integer
    LANGUAGE plpgsql IMMUTABLE
    AS $$BEGIN RETURN 2; END; $$;


ALTER FUNCTION public.test_acid() OWNER TO postgres;

--
-- Name: tham_get_random(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.tham_get_random(OUT random_out numeric) RETURNS numeric
    LANGUAGE sql
    AS $$
         SELECT random()::NUMERIC;
      $$;


ALTER FUNCTION public.tham_get_random(OUT random_out numeric) OWNER TO postgres;

--
-- Name: tham_random_date(timestamp without time zone, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.tham_random_date(date_from timestamp without time zone, date_to timestamp without time zone, OUT date_out timestamp without time zone) RETURNS timestamp without time zone
    LANGUAGE sql
    AS $$
            SELECT date_from + random() * (date_to - date_from);
         $$;


ALTER FUNCTION public.tham_random_date(date_from timestamp without time zone, date_to timestamp without time zone, OUT date_out timestamp without time zone) OWNER TO postgres;

--
-- Name: fnc_call_explain(); Type: FUNCTION; Schema: swsystem; Owner: swsystem
--

CREATE FUNCTION swsystem.fnc_call_explain() RETURNS TABLE(log_time timestamp without time zone, log_msg text)
    LANGUAGE plpgsql
    AS $$
/*
*******************************************************************************
    Object Name:   fnc_call_explain
    Purpose    :   For historical culsters manually call EXPLAIN command
    History    :
        1.0.1
            Date    :  Sep 09, 2020
            Authors : Valdis Akmens
            Notes   : Improve "fnc_call_explain" (SWDB-158)

        1.0.0
            Date    :  Jul 06, 2020
            Authors : Valdis Akmens
            Notes   : Release

    Sample run:
      SELECT * FROM swsystem.fnc_call_explain();
*******************************************************************************
*/
DECLARE
v_exec_sql          VARCHAR;
v_lock_id           BIGINT := 3334444555555666666;
v_tables            VARCHAR[]:='{"swmanagement.rounds_finished","swmanagement.rounds_history","swmanagement.spins_history"}';
v_index             INTEGER;
v_rounds_ids        VARCHAR;
v_brands_ids        VARCHAR;
V_round_id_column   VARCHAR;
v_brand_column      VARCHAR;
BEGIN
    log_time := clock_timestamp(); log_msg := 'INFO: EXPLAIN job started'; RETURN NEXT;

        /* Check if another job is running*/
    IF NOT (SELECT pg_try_advisory_xact_lock(v_lock_id)) THEN
        log_time := clock_timestamp(); log_msg := 'INFO: Another function is running. Skip'; RETURN NEXT;
                RETURN;
    END IF;

    /* Get index to choose table that will be analyzed */
    v_index:= (SELECT (random()*100)::INTEGER % (SELECT array_length(v_tables,1))+1);

        /* Check if ETL job on affected table is running*/
    IF EXISTS(SELECT * FROM hist_cluster.hist_jobs WHERE (completed_at IS NULL OR NOW() - completed_at < '5 minutes'::INTERVAL) AND table_name LIKE split_part(v_tables[v_index],'.',2)||'%') THEN
        log_time := clock_timestamp(); log_msg := 'INFO: ETL job is running. Skip'; RETURN NEXT;
                RETURN;
    END IF;

    /*Set varibles for pg_stats*/
    CASE WHEN v_tables[v_index] = 'swmanagement.rounds_finished' OR v_tables[v_index] = 'swmanagement.rounds_history' THEN
        V_round_id_column:='id';
        v_brand_column:='brand_id';
        WHEN v_tables[v_index] = 'swmanagement.spins_history' THEN
        V_round_id_column:='round_id';
        v_brand_column:='brand_id';
    END CASE;

    /*Get round_ids*/
    WITH cte AS (
        SELECT unnest(histogram_bounds::TEXT::BIGINT[]) AS ids
        FROM pg_stats
        WHERE tablename LIKE split_part(v_tables[v_index],'.',2)||'%' AND attname = V_round_id_column
    )
    SELECT string_agg(ids::VARCHAR,',')
    INTO v_rounds_ids
    FROM (
        SELECT *
        FROM cte
        ORDER BY random()
        LIMIT ROUND(random() * (10 - 1)) + 1
    ) AS x ;
    v_rounds_ids:=COALESCE(v_rounds_ids, '1');
    --RAISE INFO 'v_rounds_ids = %', v_rounds_ids;

    /*Get brand_ids*/
    WITH cte AS (
        SELECT DISTINCT unnest(histogram_bounds::TEXT::BIGINT[]) AS ids
        FROM pg_stats
        WHERE tablename LIKE split_part(v_tables[v_index],'.',2)||'%' AND attname = v_brand_column
    )
    SELECT string_agg(ids::VARCHAR,',')
    INTO v_brands_ids
    FROM (
        SELECT *
        FROM cte
        ORDER BY random()
        LIMIT ROUND(random() * (10 - 1)) + 1
    ) AS x ;
    v_brands_ids:=COALESCE(v_brands_ids, '1');
    --RAISE INFO 'v_brands_ids = %', v_brands_ids;

    v_exec_sql:='EXPLAIN SELECT * FROM '||v_tables[v_index]||' WHERE '||v_brand_column||' IN ('||v_brands_ids||') AND '||V_round_id_column||' IN ('||v_rounds_ids||');';
    --RAISE INFO 'v_exec_sql = %',v_exec_sql;

    log_time := clock_timestamp(); log_msg := 'INFO: Start '||v_tables[v_index]||' EXPLAIN. '; RETURN NEXT;
    EXECUTE v_exec_sql;
    log_time := clock_timestamp(); log_msg := 'INFO: Finish '||v_tables[v_index]||' EXPLAIN. '; RETURN NEXT;
    RETURN;
END;
$$;


ALTER FUNCTION swsystem.fnc_call_explain() OWNER TO swsystem;

--
-- Name: get_sw_hashid(character varying); Type: FUNCTION; Schema: swsystem; Owner: swsystem
--

CREATE FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) RETURNS record
    LANGUAGE plpgsql STABLE SECURITY DEFINER
    AS $$
   BEGIN
      SELECT hash_salt, hash_length FROM swsystem.sw_hashid_secret WHERE hash_project = p_hash_project INTO po_hash_salt, po_hash_length;
   END;
   $$;


ALTER FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) OWNER TO swsystem;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: hist_jobs; Type: TABLE; Schema: hist_cluster; Owner: swsystem
--

CREATE TABLE hist_cluster.hist_jobs (
    job_id bigint NOT NULL,
    action text NOT NULL,
    schema_name character varying(64) NOT NULL,
    table_name character varying(64) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    started_at timestamp without time zone,
    completed_at timestamp without time zone,
    CONSTRAINT hist_jobs_action_check CHECK ((action = ANY (ARRAY['analyze'::text, 'reindex'::text])))
);


ALTER TABLE hist_cluster.hist_jobs OWNER TO swsystem;

--
-- Name: hist_jobs_job_id_seq; Type: SEQUENCE; Schema: hist_cluster; Owner: swsystem
--

CREATE SEQUENCE hist_cluster.hist_jobs_job_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE hist_cluster.hist_jobs_job_id_seq OWNER TO swsystem;

--
-- Name: hist_jobs_job_id_seq; Type: SEQUENCE OWNED BY; Schema: hist_cluster; Owner: swsystem
--

ALTER SEQUENCE hist_cluster.hist_jobs_job_id_seq OWNED BY hist_cluster.hist_jobs.job_id;


--
-- Name: pathman_allowed_tspaces; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.pathman_allowed_tspaces (
    ts_name text NOT NULL
);


ALTER TABLE public.pathman_allowed_tspaces OWNER TO postgres;

--
-- Name: tham_locks; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.tham_locks AS
 SELECT ( SELECT pg_user.usename
           FROM pg_user
          WHERE (pg_user.usesysid = cl.relowner)) AS obj_owner,
    cl.relname AS obj_name,
        CASE cl.relkind
            WHEN 'r'::"char" THEN 'table'::character varying
            WHEN 'i'::"char" THEN 'index'::character varying
            WHEN 'v'::"char" THEN 'view'::character varying
            ELSE (cl.relkind)::character varying
        END AS obj_type,
    l.pid AS procpid,
    l.locktype,
    l.mode,
    l.granted,
    l.page,
    l.tuple,
    l.transactionid,
    l.virtualtransaction
   FROM (pg_locks l
     LEFT JOIN pg_class cl ON ((cl.oid = l.relation)))
  ORDER BY l.pid,
        CASE cl.relkind
            WHEN 'r'::"char" THEN (1)::smallint
            WHEN 'i'::"char" THEN (3)::smallint
            WHEN 'v'::"char" THEN (2)::smallint
            ELSE (4)::smallint
        END;


ALTER TABLE public.tham_locks OWNER TO postgres;

--
-- Name: tham_not_idle; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.tham_not_idle AS
 SELECT sa.pid,
    sa.usename,
    (clock_timestamp() - sa.query_start) AS time_passed,
    sa.state,
    sa.query,
    (sa.wait_event_type IS NOT NULL) AS is_waiting,
    sa.wait_event_type,
    sa.wait_event,
    sa.query_start,
    sa.backend_start,
    sa.client_addr,
    sa.client_port,
    sa.datname,
    ( SELECT count(*) AS count
           FROM pg_locks l
          WHERE ((l.pid = sa.pid) AND l.granted)) AS held_locks,
    ( SELECT count(*) AS count
           FROM pg_locks l
          WHERE ((l.pid = sa.pid) AND (NOT l.granted))) AS wait_locks,
    sa.application_name
   FROM pg_stat_activity sa
  WHERE ((sa.state <> 'idle'::text) AND ((sa.usename <> 'repmgr'::name) OR (sa.usename IS NULL)) AND (sa.query !~* 'tham_not_idle'::text));


ALTER TABLE public.tham_not_idle OWNER TO postgres;

--
-- Name: tham_replica; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.tham_replica AS
 SELECT pg_stat_replication.pid,
    pg_stat_replication.application_name,
    pg_stat_replication.backend_start,
    pg_stat_replication.state,
    pg_stat_replication.sync_state,
    pg_size_pretty(pg_wal_lsn_diff(pg_stat_replication.sent_lsn, pg_stat_replication.flush_lsn)) AS receiving_lag,
    pg_size_pretty(pg_wal_lsn_diff(pg_current_wal_lsn(), pg_stat_replication.sent_lsn)) AS sending_lag,
    pg_stat_replication.sent_lsn,
    pg_stat_replication.write_lsn,
    pg_stat_replication.flush_lsn,
    pg_current_wal_lsn() AS local_write_lsn,
    pg_walfile_name(pg_current_wal_lsn()) AS local_write_wal
   FROM pg_stat_replication;


ALTER TABLE public.tham_replica OWNER TO postgres;

--
-- Name: tham_sizes; Type: VIEW; Schema: public; Owner: postgres
--

CREATE VIEW public.tham_sizes AS
 SELECT
        CASE
            WHEN ((sub3.object_type)::text = '1.Table'::text) THEN (((('VACUUM FREEZE VERBOSE '::text || (sub3.object_owner)::text) || '."'::text) || (sub3.object_name)::text) || '";'::text)
            WHEN ((sub3.object_type)::text = '2.Index'::text) THEN (((('REINDEX INDEX '::text || (sub3.object_owner)::text) || '."'::text) || (sub3.object_name)::text) || '";'::text)
            WHEN ((sub3.object_type)::text ~~ 'TOTALS%'::text) THEN upper((sub3.object_owner)::text)
            ELSE (((COALESCE(sub3.object_owner, 'N/A'::name))::text || '.'::text) || (COALESCE(sub3.object_name, 'N/A'::name))::text)
        END AS full_name,
    sub3.object_type,
    sub3.rows_count,
    sub3.size_kb,
    sub3.tuple_size_kb,
    sub3.pages
   FROM ( SELECT sub2.object_owner,
            sub2.object_name,
            sub2.object_type,
            sub2.rows_count,
            sub2.size_kb,
            sub2.tuple_size_kb,
            sub2.pages
           FROM ( SELECT sub1.object_owner,
                    sub1.object_name,
                    sub1.object_type,
                    sub1.rows_count,
                    sub1.size_kb,
                    sub1.tuple_size_kb,
                    sub1.pages
                   FROM ( SELECT ns.nspname AS object_owner,
                            cl.relname AS object_name,
                                CASE
                                    WHEN (cl.relkind = 'r'::"char") THEN '1.Table'::character varying
                                    WHEN (cl.relkind = 'i'::"char") THEN '2.Index'::character varying
                                    WHEN (cl.relkind = 'v'::"char") THEN '5.View'::character varying
                                    WHEN (cl.relkind = 'S'::"char") THEN '4.Sequence'::character varying
                                    WHEN (cl.relkind = 't'::"char") THEN '3.Toast'::character varying
                                    ELSE NULL::character varying
                                END AS object_type,
                            (cl.reltuples)::bigint AS rows_count,
                            ((cl.relpages)::bigint * 8) AS size_kb,
                                CASE
                                    WHEN (cl.reltuples = (0)::real) THEN (0)::double precision
                                    ELSE ((((cl.relpages)::bigint * (8)::bigint))::double precision / cl.reltuples)
                                END AS tuple_size_kb,
                            (cl.relpages)::bigint AS pages
                           FROM (pg_class cl
                             JOIN pg_namespace ns ON ((cl.relnamespace = ns.oid)))
                          WHERE (((cl.relkind = 'r'::"char") OR (cl.relkind = 'i'::"char") OR (cl.relkind = 't'::"char")) AND (cl.reltuples > (9)::double precision) AND ((cl.relpages)::bigint > 9) AND (NOT (cl.relname ~~ '%_lut'::text)))) sub1
                UNION ALL
                 SELECT sub1.object_owner,
                    NULL::name AS object_name,
                    ((('TOTALS ('::text || (sub1.object_owner)::text) || '):'::text))::character varying AS object_type,
                    sum(sub1.rows_count) AS rows_count,
                    sum(sub1.size_kb) AS size_kb,
                    sum(sub1.tuple_size_kb) AS tuple_size_kb,
                    sum(sub1.pages) AS pages
                   FROM ( SELECT ns.nspname AS object_owner,
                            cl.relname AS object_name,
                                CASE
                                    WHEN (cl.relkind = 'r'::"char") THEN '1.Table'::character varying
                                    WHEN (cl.relkind = 'i'::"char") THEN '2.Index'::character varying
                                    WHEN (cl.relkind = 'v'::"char") THEN '5.View'::character varying
                                    WHEN (cl.relkind = 'S'::"char") THEN '4.Sequence'::character varying
                                    WHEN (cl.relkind = 't'::"char") THEN '3.Toast'::character varying
                                    ELSE NULL::character varying
                                END AS object_type,
                            (cl.reltuples)::bigint AS rows_count,
                            ((cl.relpages)::bigint * 8) AS size_kb,
                                CASE
                                    WHEN (cl.reltuples = (0)::real) THEN (0)::double precision
                                    ELSE ((((cl.relpages)::bigint * 8))::double precision / cl.reltuples)
                                END AS tuple_size_kb,
                            (cl.relpages)::bigint AS pages
                           FROM (pg_class cl
                             JOIN pg_namespace ns ON ((cl.relnamespace = ns.oid)))
                          WHERE (((cl.relkind = 'r'::"char") OR (cl.relkind = 'i'::"char") OR (cl.relkind = 't'::"char")) AND (cl.reltuples > (9)::double precision) AND ((cl.relpages)::bigint > 9) AND (NOT (cl.relname ~~ '%_lut'::text)))) sub1
                  GROUP BY sub1.object_owner
          ORDER BY 3, 6 DESC) sub2) sub3;


ALTER TABLE public.tham_sizes OWNER TO postgres;

--
-- Name: jp_contribution_id_seq; Type: SEQUENCE; Schema: swjackpot; Owner: swjackpot
--

CREATE SEQUENCE swjackpot.jp_contribution_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swjackpot.jp_contribution_id_seq OWNER TO swjackpot;

--
-- Name: jp_contribution_log; Type: TABLE; Schema: swjackpot; Owner: swjackpot
--

CREATE TABLE swjackpot.jp_contribution_log (
    id bigint DEFAULT nextval('swjackpot.jp_contribution_id_seq'::regclass) NOT NULL,
    trx_id character(28) NOT NULL,
    trx_date timestamp without time zone NOT NULL,
    external_id character varying(255),
    jackpot_id character varying(255) NOT NULL,
    pool character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    seed numeric DEFAULT 0 NOT NULL,
    progressive numeric DEFAULT 0 NOT NULL,
    brand_id integer NOT NULL,
    game_id character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    player_currency character(3) NOT NULL,
    contribution_amount numeric NOT NULL,
    currency_rate numeric NOT NULL,
    game_data jsonb NOT NULL,
    inserted_at timestamp without time zone DEFAULT now(),
    game_code character varying(255),
    region character varying(255),
    player_seed numeric,
    player_progressive numeric,
    round_id bigint,
    remote_trx_id character(28),
    remote_trx_region character varying(10),
    total_seed numeric,
    total_progressive numeric,
    status swjackpot.enum_jackpot_operation_status
)
WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN external_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN jackpot_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN pool SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN player_currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN remote_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_contribution_log ALTER COLUMN remote_trx_region SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.jp_contribution_log OWNER TO swjackpot;

--
-- Name: TABLE jp_contribution_log; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON TABLE swjackpot.jp_contribution_log IS 'List of contributions on jackpot pools';


--
-- Name: COLUMN jp_contribution_log.trx_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.trx_id IS 'Transaction id';


--
-- Name: COLUMN jp_contribution_log.trx_date; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.trx_date IS 'Transaction timestamp';


--
-- Name: COLUMN jp_contribution_log.external_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.external_id IS 'Transaction external id, e.g. game payment id';


--
-- Name: COLUMN jp_contribution_log.jackpot_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.jackpot_id IS 'Jackpot id';


--
-- Name: COLUMN jp_contribution_log.pool; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.pool IS 'Jackpot pool';


--
-- Name: COLUMN jp_contribution_log.currency; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.currency IS 'Jackpot currency code';


--
-- Name: COLUMN jp_contribution_log.seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.seed IS 'Jackpot pool seed change amount';


--
-- Name: COLUMN jp_contribution_log.progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.progressive IS 'Jackpot pool progressive change amount';


--
-- Name: COLUMN jp_contribution_log.brand_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.brand_id IS 'Brand whose player did contribution';


--
-- Name: COLUMN jp_contribution_log.game_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.game_id IS 'Game which contributed';


--
-- Name: COLUMN jp_contribution_log.player_code; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.player_code IS 'Player who contributed';


--
-- Name: COLUMN jp_contribution_log.player_currency; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.player_currency IS 'Player currency';


--
-- Name: COLUMN jp_contribution_log.contribution_amount; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.contribution_amount IS 'Amount of contribution in player currency';


--
-- Name: COLUMN jp_contribution_log.currency_rate; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.currency_rate IS 'Rate from player currency to jackpot currency';


--
-- Name: COLUMN jp_contribution_log.game_data; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.game_data IS 'Additional data for contribution';


--
-- Name: COLUMN jp_contribution_log.game_code; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.game_code IS 'Game code';


--
-- Name: COLUMN jp_contribution_log.region; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.region IS 'Identifier of system region, e.g. europe or asia';


--
-- Name: COLUMN jp_contribution_log.player_seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.player_seed IS 'Seed in player currency';


--
-- Name: COLUMN jp_contribution_log.player_progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.player_progressive IS 'Progressive in player currency';


--
-- Name: COLUMN jp_contribution_log.round_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.round_id IS 'Round id where jackpot is contributed';


--
-- Name: COLUMN jp_contribution_log.remote_trx_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.remote_trx_id IS 'Id of transaction executed on remote jackpot server';


--
-- Name: COLUMN jp_contribution_log.remote_trx_region; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.remote_trx_region IS 'Region code of remote jackpot server where transaction is executed';


--
-- Name: COLUMN jp_contribution_log.total_seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.total_seed IS 'Jackpot seed amount before win';


--
-- Name: COLUMN jp_contribution_log.total_progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.total_progressive IS 'Jackpot progressive amount before win';


--
-- Name: COLUMN jp_contribution_log.status; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_contribution_log.status IS 'JP contribution status';


--
-- Name: jp_wallet_operation_log; Type: TABLE; Schema: swjackpot; Owner: swjackpot
--

CREATE TABLE swjackpot.jp_wallet_operation_log (
    id bigint NOT NULL,
    operation_id integer NOT NULL,
    operation_name character varying(255),
    public_id character(28) NOT NULL,
    external_trx_id character varying(255),
    is_external boolean NOT NULL,
    game_id character varying(255),
    ts timestamp without time zone NOT NULL,
    version integer NOT NULL,
    data jsonb NOT NULL,
    params jsonb,
    inserted_at timestamp without time zone DEFAULT now(),
    committed_at timestamp without time zone
)
WITH (fillfactor='100');
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN operation_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN public_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN external_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.jp_wallet_operation_log ALTER COLUMN params SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.jp_wallet_operation_log OWNER TO swjackpot;

--
-- Name: COLUMN jp_wallet_operation_log.committed_at; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.jp_wallet_operation_log.committed_at IS 'Timestamp when operation was committed';


--
-- Name: remote_jp_contribution_log_id_seq; Type: SEQUENCE; Schema: swjackpot; Owner: swjackpot
--

CREATE SEQUENCE swjackpot.remote_jp_contribution_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swjackpot.remote_jp_contribution_log_id_seq OWNER TO swjackpot;

--
-- Name: remote_jp_contribution_log; Type: TABLE; Schema: swjackpot; Owner: swjackpot
--

CREATE TABLE swjackpot.remote_jp_contribution_log (
    id bigint DEFAULT nextval('swjackpot.remote_jp_contribution_log_id_seq'::regclass) NOT NULL,
    trx_id character(28) NOT NULL,
    trx_date timestamp without time zone NOT NULL,
    external_id character varying(255),
    remote_trx_id character(28),
    remote_trx_region character varying(10),
    round_id bigint NOT NULL,
    jackpot_id character varying(255) NOT NULL,
    pool character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    seed numeric DEFAULT 0 NOT NULL,
    progressive numeric DEFAULT 0 NOT NULL,
    brand_id integer NOT NULL,
    region character varying(255),
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    player_currency character(3) NOT NULL,
    contribution_amount numeric NOT NULL,
    currency_rate numeric NOT NULL,
    game_data jsonb NOT NULL,
    player_seed numeric,
    player_progressive numeric,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    total_seed numeric,
    total_progressive numeric
);
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN external_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN remote_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN remote_trx_region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN jackpot_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN pool SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN region SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN player_currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swjackpot.remote_jp_contribution_log ALTER COLUMN game_data SET STORAGE EXTERNAL;


ALTER TABLE swjackpot.remote_jp_contribution_log OWNER TO swjackpot;

--
-- Name: TABLE remote_jp_contribution_log; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON TABLE swjackpot.remote_jp_contribution_log IS 'List of contributions on jackpot pools received from remote jackpot server';


--
-- Name: COLUMN remote_jp_contribution_log.trx_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.trx_id IS 'Transaction id';


--
-- Name: COLUMN remote_jp_contribution_log.trx_date; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.trx_date IS 'Transaction timestamp';


--
-- Name: COLUMN remote_jp_contribution_log.external_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.external_id IS 'Transaction external id, e.g. game payment id';


--
-- Name: COLUMN remote_jp_contribution_log.remote_trx_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.remote_trx_id IS 'Id of transaction executed on remote jackpot server';


--
-- Name: COLUMN remote_jp_contribution_log.remote_trx_region; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.remote_trx_region IS 'Region code of remote jackpot server where transaction is executed';


--
-- Name: COLUMN remote_jp_contribution_log.round_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.round_id IS 'Round id where jackpot is contributed';


--
-- Name: COLUMN remote_jp_contribution_log.jackpot_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.jackpot_id IS 'Jackpot id';


--
-- Name: COLUMN remote_jp_contribution_log.pool; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.pool IS 'Jackpot pool';


--
-- Name: COLUMN remote_jp_contribution_log.currency; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.currency IS 'Jackpot currency code';


--
-- Name: COLUMN remote_jp_contribution_log.seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.seed IS 'Jackpot pool seed change amount';


--
-- Name: COLUMN remote_jp_contribution_log.progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.progressive IS 'Jackpot pool progressive change amount';


--
-- Name: COLUMN remote_jp_contribution_log.brand_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.brand_id IS 'Brand whose player did contribution';


--
-- Name: COLUMN remote_jp_contribution_log.region; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.region IS 'Identifier of system region, e.g. europe or asia';


--
-- Name: COLUMN remote_jp_contribution_log.game_id; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_id IS 'Game which contributed';


--
-- Name: COLUMN remote_jp_contribution_log.game_code; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_code IS 'Game code';


--
-- Name: COLUMN remote_jp_contribution_log.player_code; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_code IS 'Player who contributed';


--
-- Name: COLUMN remote_jp_contribution_log.player_currency; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_currency IS 'Player currency';


--
-- Name: COLUMN remote_jp_contribution_log.contribution_amount; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.contribution_amount IS 'Amount of contribution in player currency';


--
-- Name: COLUMN remote_jp_contribution_log.currency_rate; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.currency_rate IS 'Rate from player currency to jackpot currency';


--
-- Name: COLUMN remote_jp_contribution_log.game_data; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.game_data IS 'Additional data for contribution';


--
-- Name: COLUMN remote_jp_contribution_log.player_seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_seed IS 'Seed in player currency';


--
-- Name: COLUMN remote_jp_contribution_log.player_progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.player_progressive IS 'Progressive in player currency';


--
-- Name: COLUMN remote_jp_contribution_log.inserted_at; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.inserted_at IS 'When transaction is inserted';


--
-- Name: COLUMN remote_jp_contribution_log.total_seed; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.total_seed IS 'Jackpot seed amount before win';


--
-- Name: COLUMN remote_jp_contribution_log.total_progressive; Type: COMMENT; Schema: swjackpot; Owner: swjackpot
--

COMMENT ON COLUMN swjackpot.remote_jp_contribution_log.total_progressive IS 'Jackpot progressive amount before win';


--
-- Name: audits; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.audits (
    audit_id integer NOT NULL,
    entity_id integer NOT NULL,
    ts timestamp without time zone NOT NULL,
    audits_summary_id smallint,
    history jsonb,
    initiator_type swmanagement.enum_audits_initiator_type NOT NULL,
    initiator_name character varying(255) NOT NULL,
    ip inet NOT NULL,
    user_agent character varying(2048) NOT NULL,
    initiator_service_name character varying(255),
    audit_type smallint,
    initiator_issue_id character varying(255)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN history SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN initiator_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN user_agent SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN initiator_service_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.audits ALTER COLUMN initiator_issue_id SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.audits OWNER TO swmanagement;

--
-- Name: COLUMN audits.initiator_issue_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.audits.initiator_issue_id IS 'Initiator Jira issue';


--
-- Name: audits_audit_id_seq; Type: SEQUENCE; Schema: swmanagement; Owner: swmanagement
--

CREATE SEQUENCE swmanagement.audits_audit_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.audits_audit_id_seq OWNER TO swmanagement;

--
-- Name: audits_audit_id_seq; Type: SEQUENCE OWNED BY; Schema: swmanagement; Owner: swmanagement
--

ALTER SEQUENCE swmanagement.audits_audit_id_seq OWNED BY swmanagement.audits.audit_id;


--
-- Name: currency_rates; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.currency_rates (
    currency_code character(3) NOT NULL,
    rate_date date NOT NULL,
    rate numeric NOT NULL,
    ts integer NOT NULL
);


ALTER TABLE swmanagement.currency_rates OWNER TO swmanagement;

--
-- Name: games; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.games (
    id integer NOT NULL,
    code character varying(255),
    type character varying NOT NULL,
    url character varying(1024),
    provider_id integer,
    provider_game_code character varying(255) NOT NULL,
    status character varying NOT NULL,
    default_info jsonb,
    info jsonb,
    limits jsonb,
    version integer,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    comment character varying(255),
    title character varying(255),
    features jsonb,
    history_render_type integer DEFAULT 0 NOT NULL,
    history_url character varying(255),
    limits_group character varying(255),
    countries jsonb,
    schema_definition_id integer,
    total_bet_multiplier numeric,
    client_features jsonb
);


ALTER TABLE swmanagement.games OWNER TO swmanagement;

--
-- Name: COLUMN games.comment; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.comment IS 'Game comment';


--
-- Name: COLUMN games.features; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.features IS 'Game specific features';


--
-- Name: COLUMN games.history_render_type; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.history_render_type IS 'Game history render type';


--
-- Name: COLUMN games.history_url; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.history_url IS 'Priority history url of game';


--
-- Name: COLUMN games.limits_group; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.limits_group IS 'The criteria for grouping games with equal limits';


--
-- Name: COLUMN games.countries; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.countries IS 'list of allowed countries for game. If empty/null - no restrictions';


--
-- Name: COLUMN games.schema_definition_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.schema_definition_id IS 'Schema definitions reference';


--
-- Name: COLUMN games.total_bet_multiplier; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.total_bet_multiplier IS 'Total bet multiplier';


--
-- Name: COLUMN games.client_features; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.games.client_features IS 'Stores client features (turbo, fastPlay) which will be used when starting the game';


--
-- Name: payments; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.payments (
    id bigint NOT NULL,
    trx_id character(28) NOT NULL,
    ext_trx_id character varying(255),
    brand_id character varying(255) NOT NULL,
    player_code character varying(255) NOT NULL,
    order_id character varying(255),
    order_date timestamp without time zone NOT NULL,
    order_info jsonb,
    order_status character varying(255) NOT NULL,
    order_type character varying(255) NOT NULL,
    payment_method_code character varying(255),
    currency_code character(3) NOT NULL,
    amount numeric NOT NULL,
    start_date timestamp without time zone NOT NULL,
    end_date timestamp without time zone,
    processed_by character varying(255),
    marks jsonb,
    is_test boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    player_balance_after numeric
);


ALTER TABLE swmanagement.payments OWNER TO swmanagement;

--
-- Name: COLUMN payments.player_balance_after; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.payments.player_balance_after IS 'Player balance after payment';


--
-- Name: payments_id_seq; Type: SEQUENCE; Schema: swmanagement; Owner: swmanagement
--

CREATE SEQUENCE swmanagement.payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.payments_id_seq OWNER TO swmanagement;

--
-- Name: payments_id_seq; Type: SEQUENCE OWNED BY; Schema: swmanagement; Owner: swmanagement
--

ALTER SEQUENCE swmanagement.payments_id_seq OWNED BY swmanagement.payments.id;


--
-- Name: rounds_finished; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.rounds_finished (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    total_win numeric DEFAULT 0 NOT NULL,
    total_bet numeric DEFAULT 0 NOT NULL,
    balance_before numeric,
    balance_after numeric,
    total_events numeric DEFAULT 0 NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone NOT NULL,
    test boolean DEFAULT false NOT NULL,
    session_id bigint,
    inserted_at timestamp without time zone DEFAULT now(),
    recovery_type swmanagement.enum_recovery_type,
    total_jp_contribution numeric,
    total_jp_win numeric,
    debit numeric,
    credit numeric,
    ctrl integer,
    extra_data jsonb,
    operator_site_id integer
);
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_finished ALTER COLUMN currency SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.rounds_finished OWNER TO swmanagement;

--
-- Name: TABLE rounds_finished; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON TABLE swmanagement.rounds_finished IS 'Finisched rounds statistics';


--
-- Name: COLUMN rounds_finished.id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.id IS 'Round serial identifier. Generated inside gameserver';


--
-- Name: COLUMN rounds_finished.brand_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.brand_id IS 'Brand identifier';


--
-- Name: COLUMN rounds_finished.player_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.player_code IS 'Player code';


--
-- Name: COLUMN rounds_finished.game_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.game_id IS 'Game identifier. Is used to load appropriate game module';


--
-- Name: COLUMN rounds_finished.game_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.game_code IS 'Game code in management system';


--
-- Name: COLUMN rounds_finished.device_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.device_id IS 'Device from user was playing.';


--
-- Name: COLUMN rounds_finished.currency; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.currency IS 'Currency code';


--
-- Name: COLUMN rounds_finished.total_win; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.total_win IS 'Total win amount in round';


--
-- Name: COLUMN rounds_finished.total_bet; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.total_bet IS 'Total bet amount in round';


--
-- Name: COLUMN rounds_finished.balance_before; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.balance_before IS 'Balance at the begining of round';


--
-- Name: COLUMN rounds_finished.balance_after; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.balance_after IS 'Balance at the end of round';


--
-- Name: COLUMN rounds_finished.total_events; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.total_events IS 'Total count of player events/actions';


--
-- Name: COLUMN rounds_finished.started_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.started_at IS 'Timestamp when round was started';


--
-- Name: COLUMN rounds_finished.finished_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.finished_at IS 'Timestamp when round was finished';


--
-- Name: COLUMN rounds_finished.test; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.test IS 'Is it test game';


--
-- Name: COLUMN rounds_finished.recovery_type; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.recovery_type IS 'The recovery type if the round was recoveried by operator/support';


--
-- Name: COLUMN rounds_finished.debit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.debit IS 'Debit amount (transfer-in amount, etc)';


--
-- Name: COLUMN rounds_finished.credit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';


--
-- Name: COLUMN rounds_finished.ctrl; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.ctrl IS 'Control sum (random) to check logical duplicates';


--
-- Name: COLUMN rounds_finished.operator_site_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_finished.operator_site_id IS 'Operator''s site id';


--
-- Name: rounds_history; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.rounds_history (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency character(3) NOT NULL,
    total_win numeric DEFAULT 0 NOT NULL,
    total_bet numeric DEFAULT 0 NOT NULL,
    balance_before numeric,
    balance_after numeric,
    total_events numeric DEFAULT 0 NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone,
    test boolean DEFAULT false NOT NULL,
    session_id bigint,
    inserted_at timestamp without time zone DEFAULT now(),
    recovery_type swmanagement.enum_recovery_type,
    total_jp_contribution numeric,
    total_jp_win numeric,
    debit numeric,
    credit numeric,
    ctrl integer,
    extra_data jsonb,
    operator_site_id integer
);
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.rounds_history ALTER COLUMN currency SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.rounds_history OWNER TO swmanagement;

--
-- Name: TABLE rounds_history; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON TABLE swmanagement.rounds_history IS 'Rounds statistics';


--
-- Name: COLUMN rounds_history.id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.id IS 'Round serial identifier. Generated inside gameserver.';


--
-- Name: COLUMN rounds_history.brand_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.brand_id IS 'Brand identifier';


--
-- Name: COLUMN rounds_history.player_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.player_code IS 'Player code';


--
-- Name: COLUMN rounds_history.game_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.game_id IS 'Game identifier. Is used to load appropriate game module';


--
-- Name: COLUMN rounds_history.game_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.game_code IS 'Game code in management system';


--
-- Name: COLUMN rounds_history.device_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.device_id IS 'Device from user was playing.';


--
-- Name: COLUMN rounds_history.currency; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.currency IS 'Currency code';


--
-- Name: COLUMN rounds_history.total_win; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.total_win IS 'Total win amount in round';


--
-- Name: COLUMN rounds_history.total_bet; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.total_bet IS 'Total bet amount in round';


--
-- Name: COLUMN rounds_history.balance_before; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.balance_before IS 'Balance at the begining of round';


--
-- Name: COLUMN rounds_history.balance_after; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.balance_after IS 'Balance at the end of round';


--
-- Name: COLUMN rounds_history.total_events; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.total_events IS 'Total count of player events/actions';


--
-- Name: COLUMN rounds_history.started_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.started_at IS 'Timestamp when round was started';


--
-- Name: COLUMN rounds_history.finished_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.finished_at IS 'Timestamp when round was finished';


--
-- Name: COLUMN rounds_history.test; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.test IS 'Is it test game';


--
-- Name: COLUMN rounds_history.recovery_type; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.recovery_type IS 'The recovery type if the round was recoveried by operator/support';


--
-- Name: COLUMN rounds_history.total_jp_contribution; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.total_jp_contribution IS 'Total jackpot contribution per round in player currency';


--
-- Name: COLUMN rounds_history.total_jp_win; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.total_jp_win IS 'Total jackpot win per round in player currency';


--
-- Name: COLUMN rounds_history.debit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.debit IS 'Debit amount (transfer-in amount, etc)';


--
-- Name: COLUMN rounds_history.credit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';


--
-- Name: COLUMN rounds_history.ctrl; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.ctrl IS 'Control sum (random) to check logical duplicates';


--
-- Name: COLUMN rounds_history.operator_site_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.rounds_history.operator_site_id IS 'Operator''s site id';


--
-- Name: sessions_history; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.sessions_history (
    id bigint NOT NULL,
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_version character varying(255),
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    currency_code character(3) NOT NULL,
    player_country character varying(32),
    player_language character varying(32),
    user_agent_id integer,
    user_agent character varying(2048),
    screen_size character varying(255),
    is_broken boolean DEFAULT false NOT NULL,
    is_test boolean DEFAULT false NOT NULL,
    started_at timestamp without time zone NOT NULL,
    finished_at timestamp without time zone,
    inserted_at timestamp without time zone DEFAULT now(),
    browser character varying(255),
    os character varying(255),
    platform character varying(255),
    played_from_country character varying(6),
    ip inet,
    browser_version character varying(255),
    ctrl integer,
    interruption_reason swmanagement.enum_sessions_history_interruption_reason,
    referrer character varying(255),
    operator_site_id integer,
    ext_session_id character varying(255),
    operator_country character varying(6)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_version SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN currency_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_country SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN player_language SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN user_agent SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN screen_size SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN browser SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN os SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN platform SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.sessions_history ALTER COLUMN browser_version SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.sessions_history OWNER TO swmanagement;

--
-- Name: TABLE sessions_history; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON TABLE swmanagement.sessions_history IS 'Player session info';


--
-- Name: COLUMN sessions_history.id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.id IS 'Session serial identifier. Generated inside gameserver.';


--
-- Name: COLUMN sessions_history.brand_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.brand_id IS 'Brand identifier';


--
-- Name: COLUMN sessions_history.player_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.player_code IS 'Player code';


--
-- Name: COLUMN sessions_history.game_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.game_id IS 'Game identifier. Is used to load appropriate game module';


--
-- Name: COLUMN sessions_history.game_version; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.game_version IS 'Game module version';


--
-- Name: COLUMN sessions_history.game_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.game_code IS 'Game code in management system';


--
-- Name: COLUMN sessions_history.device_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.device_id IS 'Device from which user was playing';


--
-- Name: COLUMN sessions_history.currency_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.currency_code IS 'Player currency code';


--
-- Name: COLUMN sessions_history.player_country; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.player_country IS 'Player country';


--
-- Name: COLUMN sessions_history.player_language; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.player_language IS 'Player language';


--
-- Name: COLUMN sessions_history.user_agent_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.user_agent_id IS 'Reference to user_agents dictionary';


--
-- Name: COLUMN sessions_history.user_agent; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.user_agent IS 'Full user agent string. To be used to extract detailed device info';


--
-- Name: COLUMN sessions_history.screen_size; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.screen_size IS 'Screen size of player device';


--
-- Name: COLUMN sessions_history.is_broken; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.is_broken IS 'If session closed with unfinished round or broken payment';


--
-- Name: COLUMN sessions_history.is_test; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.is_test IS 'Is it test game';


--
-- Name: COLUMN sessions_history.started_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.started_at IS 'Timestamp when session was started';


--
-- Name: COLUMN sessions_history.finished_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.finished_at IS 'Timestamp when session was finished';


--
-- Name: COLUMN sessions_history.browser; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.browser IS 'Game client browser';


--
-- Name: COLUMN sessions_history.os; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.os IS 'Game client operating system';


--
-- Name: COLUMN sessions_history.platform; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.platform IS 'Game client platform';


--
-- Name: COLUMN sessions_history.played_from_country; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.played_from_country IS 'Country player played from';


--
-- Name: COLUMN sessions_history.ip; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.ip IS 'IP player played from';


--
-- Name: COLUMN sessions_history.browser_version; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.browser_version IS 'Version of the browser';


--
-- Name: COLUMN sessions_history.ctrl; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.ctrl IS 'Control sum (random) to check logical duplicates';


--
-- Name: COLUMN sessions_history.interruption_reason; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.interruption_reason IS 'The reason why session has been interrupted';


--
-- Name: COLUMN sessions_history.referrer; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.referrer IS 'Operator''s site url that player comes from';


--
-- Name: COLUMN sessions_history.operator_site_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.operator_site_id IS 'Operator''s site id';


--
-- Name: COLUMN sessions_history.ext_session_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.ext_session_id IS 'Operator''s session id';


--
-- Name: COLUMN sessions_history.operator_country; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.sessions_history.operator_country IS 'Country player from operator';


--
-- Name: special_force_wal; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.special_force_wal (
    id bigint,
    brand_id integer,
    player_code character varying(255),
    game_id character varying(255),
    game_code character varying(255),
    device_id character varying(255),
    currency character(3),
    total_win numeric,
    total_bet numeric,
    balance_before numeric,
    balance_after numeric,
    total_events numeric,
    started_at timestamp without time zone,
    finished_at timestamp without time zone,
    test boolean,
    session_id bigint,
    inserted_at timestamp without time zone,
    recovery_type swmanagement.enum_recovery_type
);


ALTER TABLE swmanagement.special_force_wal OWNER TO swmanagement;

--
-- Name: spinhistories_seq; Type: SEQUENCE; Schema: swmanagement; Owner: swmanagement
--

CREATE SEQUENCE swmanagement.spinhistories_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.spinhistories_seq OWNER TO swmanagement;

--
-- Name: spins_history; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.spins_history (
    brand_id integer NOT NULL,
    player_code character varying(255) NOT NULL,
    game_id character varying(255) NOT NULL,
    game_code character varying(255) NOT NULL,
    device_id character varying(255) NOT NULL,
    round_ended boolean NOT NULL,
    spin_serial_number integer NOT NULL,
    wallet_transaction_id character varying(255),
    currency character(3) NOT NULL,
    win numeric NOT NULL,
    bet numeric NOT NULL,
    round_id bigint NOT NULL,
    ts timestamp without time zone NOT NULL,
    result jsonb NOT NULL,
    test boolean DEFAULT false NOT NULL,
    spin_history_id bigint DEFAULT nextval('swmanagement.spinhistories_seq'::regclass),
    game_type character varying(50),
    user_agent_id integer,
    balance_before numeric,
    balance_after numeric,
    game_version character varying(20),
    inserted_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    session_id bigint,
    total_jp_contribution numeric,
    total_jp_win numeric,
    extra_data jsonb,
    debit numeric,
    credit numeric,
    ctrl integer,
    free_bet_coin numeric,
    lobby_session_id character varying(255)
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN device_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN wallet_transaction_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN result SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_type SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN game_version SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.spins_history ALTER COLUMN extra_data SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.spins_history OWNER TO swmanagement;

--
-- Name: COLUMN spins_history.user_agent_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.user_agent_id IS 'user agent link from user_agents dictionary';


--
-- Name: COLUMN spins_history.balance_before; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.balance_before IS 'Balance befor player action';


--
-- Name: COLUMN spins_history.balance_after; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.balance_after IS 'Balance after player action';


--
-- Name: COLUMN spins_history.game_version; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.game_version IS 'Game module version';


--
-- Name: COLUMN spins_history.session_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.session_id IS 'Reference to game session';


--
-- Name: COLUMN spins_history.total_jp_contribution; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.total_jp_contribution IS 'Total jackpot contribution per spin in player currency';


--
-- Name: COLUMN spins_history.total_jp_win; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.total_jp_win IS 'Total jackpot win in spin per player currency';


--
-- Name: COLUMN spins_history.extra_data; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.extra_data IS 'Additional spin/round data to store';


--
-- Name: COLUMN spins_history.debit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.debit IS 'Debit amount (transfer-in amount, etc)';


--
-- Name: COLUMN spins_history.credit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.credit IS 'Credit amount (redeem bns amount, transfer-out amount, etc)';


--
-- Name: COLUMN spins_history.ctrl; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.ctrl IS 'Control sum (random) to check logical duplicates';


--
-- Name: COLUMN spins_history.free_bet_coin; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.free_bet_coin IS 'Information about real coin bet value';


--
-- Name: COLUMN spins_history.lobby_session_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.spins_history.lobby_session_id IS 'Optional player lobby session Id';


--
-- Name: wallet_entity_payment_log_id_seq; Type: SEQUENCE; Schema: swmanagement; Owner: swmanagement
--

CREATE SEQUENCE swmanagement.wallet_entity_payment_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.wallet_entity_payment_log_id_seq OWNER TO swmanagement;

--
-- Name: wallet_entity_payment_log; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.wallet_entity_payment_log (
    id bigint DEFAULT nextval('swmanagement.wallet_entity_payment_log_id_seq'::regclass) NOT NULL,
    transfer_from character varying(255),
    transfer_to character varying(255),
    amount numeric DEFAULT 0 NOT NULL,
    currency character(3) NOT NULL,
    ts timestamp without time zone NOT NULL,
    transfer_type swmanagement.enum_wallet_entity_payment_log_transfer_type NOT NULL,
    is_test boolean DEFAULT false NOT NULL,
    inserted_at timestamp without time zone DEFAULT now(),
    initiator_name character varying(255)
);
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN transfer_from SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN transfer_to SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_entity_payment_log ALTER COLUMN initiator_name SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.wallet_entity_payment_log OWNER TO swmanagement;

--
-- Name: TABLE wallet_entity_payment_log; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON TABLE swmanagement.wallet_entity_payment_log IS 'Table for storing money transfers from and to entity';


--
-- Name: COLUMN wallet_entity_payment_log.transfer_from; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_from IS 'Entity id or player code of money transferred from';


--
-- Name: COLUMN wallet_entity_payment_log.transfer_to; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_to IS 'Entity id or player code of money transferred to';


--
-- Name: COLUMN wallet_entity_payment_log.transfer_type; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.transfer_type IS 'Constraint to mark transfer direction (entity->player & etc...)';


--
-- Name: COLUMN wallet_entity_payment_log.initiator_name; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_entity_payment_log.initiator_name IS 'Username of account, that initiated transfer';


--
-- Name: wallet_operation_log; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.wallet_operation_log (
    id bigint NOT NULL,
    operation_id integer NOT NULL,
    operation_name character varying(255),
    public_id character(28) NOT NULL,
    external_trx_id character varying(255),
    is_external boolean NOT NULL,
    game_id character varying(255),
    ts timestamp without time zone NOT NULL,
    version integer NOT NULL,
    data jsonb NOT NULL,
    params jsonb,
    inserted_at timestamp without time zone DEFAULT now(),
    committed_at timestamp without time zone
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN operation_name SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN public_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN external_trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN data SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_operation_log ALTER COLUMN params SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.wallet_operation_log OWNER TO swmanagement;

--
-- Name: COLUMN wallet_operation_log.params; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_operation_log.params IS 'List of optional params';


--
-- Name: COLUMN wallet_operation_log.committed_at; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_operation_log.committed_at IS 'Timestamp when operation was committed';


--
-- Name: wallet_win_bet; Type: TABLE; Schema: swmanagement; Owner: swmanagement
--

CREATE TABLE swmanagement.wallet_win_bet (
    id bigint NOT NULL,
    trx_id character(28),
    brand_id integer,
    currency character(3),
    payment_date timestamp without time zone NOT NULL,
    bet numeric DEFAULT 0 NOT NULL,
    win numeric DEFAULT 0 NOT NULL,
    bet_rollback boolean DEFAULT false NOT NULL,
    game_id character varying(255),
    player_code character varying,
    game_code character varying(255),
    transaction_type swmanagement.enum_wallet_win_bet_transaction_type,
    is_test boolean DEFAULT false,
    inserted_at timestamp without time zone DEFAULT now(),
    debit numeric,
    credit numeric,
    round_ended boolean DEFAULT false,
    sub_trx_type character varying(255),
    ggr_calculation swmanagement.enum_wallet_win_bet_ggr_calculation,
    round_wins numeric,
    round_bets numeric
)
WITH (fillfactor='100');
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN trx_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN currency SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN game_id SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN player_code SET STORAGE EXTERNAL;
ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN game_code SET STORAGE EXTERNAL;


ALTER TABLE swmanagement.wallet_win_bet OWNER TO swmanagement;

--
-- Name: COLUMN wallet_win_bet.bet_rollback; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.bet_rollback IS 'Marks transaction as rollback transaction - win and bet were restored on customer account';


--
-- Name: COLUMN wallet_win_bet.game_id; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.game_id IS 'Holds game id on a spin';


--
-- Name: COLUMN wallet_win_bet.player_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.player_code IS 'Stores player codes. Not a reference';


--
-- Name: COLUMN wallet_win_bet.game_code; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.game_code IS 'Game code needed for filtering by games';


--
-- Name: COLUMN wallet_win_bet.is_test; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.is_test IS 'Is it a test player';


--
-- Name: COLUMN wallet_win_bet.debit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.debit IS 'Debit amount (transfer-in amount, etc)';


--
-- Name: COLUMN wallet_win_bet.credit; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.credit IS 'Credit amount (redeem bns amount, transfer-out, grc redeem amount, etc)';


--
-- Name: COLUMN wallet_win_bet.round_ended; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.round_ended IS 'Flat that is true if round was ended on this bet/win operation';


--
-- Name: COLUMN wallet_win_bet.sub_trx_type; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.sub_trx_type IS 'Sub type of bonus payment (ph-tournament, bonus_coins_redeem, etc)';


--
-- Name: COLUMN wallet_win_bet.ggr_calculation; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.ggr_calculation IS 'Type of ggr calculation';


--
-- Name: COLUMN wallet_win_bet.round_wins; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.round_wins IS 'Total win of round (excluding jackpot wins)';


--
-- Name: COLUMN wallet_win_bet.round_bets; Type: COMMENT; Schema: swmanagement; Owner: swmanagement
--

COMMENT ON COLUMN swmanagement.wallet_win_bet.round_bets IS 'Total bet of round';


--
-- Name: wallet_win_bet_id_seq; Type: SEQUENCE; Schema: swmanagement; Owner: swmanagement
--

CREATE SEQUENCE swmanagement.wallet_win_bet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE swmanagement.wallet_win_bet_id_seq OWNER TO swmanagement;

--
-- Name: wallet_win_bet_id_seq; Type: SEQUENCE OWNED BY; Schema: swmanagement; Owner: swmanagement
--

ALTER SEQUENCE swmanagement.wallet_win_bet_id_seq OWNED BY swmanagement.wallet_win_bet.id;


--
-- Name: databasechangelog; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.databasechangelog (
    id character varying(255) NOT NULL,
    author character varying(255) NOT NULL,
    filename character varying(255) NOT NULL,
    dateexecuted timestamp without time zone NOT NULL,
    orderexecuted integer NOT NULL,
    exectype character varying(10) NOT NULL,
    md5sum character varying(35),
    description character varying(255),
    comments character varying(255),
    tag character varying(255),
    liquibase character varying(20),
    contexts character varying(255),
    labels character varying(255),
    deployment_id character varying(10)
);


ALTER TABLE swsystem.databasechangelog OWNER TO swsystem;

--
-- Name: databasechangeloglock; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.databasechangeloglock (
    id integer NOT NULL,
    locked boolean NOT NULL,
    lockgranted timestamp without time zone,
    lockedby character varying(255)
);


ALTER TABLE swsystem.databasechangeloglock OWNER TO swsystem;

--
-- Name: hist_arch_01_tables; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.hist_arch_01_tables (
    schema_name character varying(255),
    parent_name character varying(255),
    table_name character varying(255),
    table_size_with_idx character varying(50),
    table_size character varying(50),
    partition_nr integer,
    status character varying(50)
);


ALTER TABLE swsystem.hist_arch_01_tables OWNER TO swsystem;

--
-- Name: replica_ticker; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.replica_ticker (
    ts timestamp without time zone
);


ALTER TABLE swsystem.replica_ticker OWNER TO swsystem;

--
-- Name: sw_hashid_secret; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.sw_hashid_secret (
    hash_project character varying(20) NOT NULL,
    hash_salt text NOT NULL,
    hash_length integer NOT NULL
);


ALTER TABLE swsystem.sw_hashid_secret OWNER TO swsystem;

--
-- Name: hist_jobs job_id; Type: DEFAULT; Schema: hist_cluster; Owner: swsystem
--

ALTER TABLE ONLY hist_cluster.hist_jobs ALTER COLUMN job_id SET DEFAULT nextval('hist_cluster.hist_jobs_job_id_seq'::regclass);


--
-- Name: audits audit_id; Type: DEFAULT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.audits ALTER COLUMN audit_id SET DEFAULT nextval('swmanagement.audits_audit_id_seq'::regclass);


--
-- Name: payments id; Type: DEFAULT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.payments ALTER COLUMN id SET DEFAULT nextval('swmanagement.payments_id_seq'::regclass);


--
-- Name: wallet_win_bet id; Type: DEFAULT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.wallet_win_bet ALTER COLUMN id SET DEFAULT nextval('swmanagement.wallet_win_bet_id_seq'::regclass);


--
-- Name: hist_jobs hist_jobs_pkey; Type: CONSTRAINT; Schema: hist_cluster; Owner: swsystem
--

ALTER TABLE ONLY hist_cluster.hist_jobs
    ADD CONSTRAINT hist_jobs_pkey PRIMARY KEY (job_id);


--
-- Name: pathman_allowed_tspaces pathman_allowed_tspaces_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pathman_allowed_tspaces
    ADD CONSTRAINT pathman_allowed_tspaces_pkey PRIMARY KEY (ts_name);


--
-- Name: jp_contribution_log jp_contribution_log_pkey; Type: CONSTRAINT; Schema: swjackpot; Owner: swjackpot
--

ALTER TABLE ONLY swjackpot.jp_contribution_log
    ADD CONSTRAINT jp_contribution_log_pkey PRIMARY KEY (id) WITH (fillfactor='100');


--
-- Name: jp_wallet_operation_log jp_wallet_operation_log_pkey; Type: CONSTRAINT; Schema: swjackpot; Owner: swjackpot
--

ALTER TABLE ONLY swjackpot.jp_wallet_operation_log
    ADD CONSTRAINT jp_wallet_operation_log_pkey PRIMARY KEY (id, operation_id) WITH (fillfactor='100');


--
-- Name: remote_jp_contribution_log remote_jp_contribution_log_pkey; Type: CONSTRAINT; Schema: swjackpot; Owner: swjackpot
--

ALTER TABLE ONLY swjackpot.remote_jp_contribution_log
    ADD CONSTRAINT remote_jp_contribution_log_pkey PRIMARY KEY (id);


--
-- Name: audits audits_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.audits
    ADD CONSTRAINT audits_pkey PRIMARY KEY (audit_id) WITH (fillfactor='100');


--
-- Name: currency_rates currency_rates_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.currency_rates
    ADD CONSTRAINT currency_rates_pkey PRIMARY KEY (currency_code, rate_date);


--
-- Name: games games_code_key; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.games
    ADD CONSTRAINT games_code_key UNIQUE (code);


--
-- Name: games games_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.games
    ADD CONSTRAINT games_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: payments payments_trx_id_key; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.payments
    ADD CONSTRAINT payments_trx_id_key UNIQUE (trx_id);


--
-- Name: rounds_finished rounds_finished_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.rounds_finished
    ADD CONSTRAINT rounds_finished_pkey PRIMARY KEY (id);


--
-- Name: rounds_history rounds_history_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.rounds_history
    ADD CONSTRAINT rounds_history_pkey PRIMARY KEY (id) WITH (fillfactor='100');


--
-- Name: sessions_history sessions_history_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.sessions_history
    ADD CONSTRAINT sessions_history_pkey PRIMARY KEY (id) WITH (fillfactor='100');


--
-- Name: spins_history spins_history_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.spins_history
    ADD CONSTRAINT spins_history_pkey PRIMARY KEY (brand_id, player_code, game_code, device_id, spin_serial_number, round_id) WITH (fillfactor='100');


--
-- Name: wallet_entity_payment_log wallet_entity_payment_log_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.wallet_entity_payment_log
    ADD CONSTRAINT wallet_entity_payment_log_pkey PRIMARY KEY (id);


--
-- Name: wallet_operation_log wallet_operation_log_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.wallet_operation_log
    ADD CONSTRAINT wallet_operation_log_pkey PRIMARY KEY (id, operation_id) WITH (fillfactor='100');


--
-- Name: wallet_win_bet wallet_win_bet_pkey; Type: CONSTRAINT; Schema: swmanagement; Owner: swmanagement
--

ALTER TABLE ONLY swmanagement.wallet_win_bet
    ADD CONSTRAINT wallet_win_bet_pkey PRIMARY KEY (id) WITH (fillfactor='100');


--
-- Name: databasechangeloglock pk_databasechangeloglock; Type: CONSTRAINT; Schema: swsystem; Owner: swsystem
--

ALTER TABLE ONLY swsystem.databasechangeloglock
    ADD CONSTRAINT pk_databasechangeloglock PRIMARY KEY (id);


--
-- Name: sw_hashid_secret sw_hashid_secret_pkey; Type: CONSTRAINT; Schema: swsystem; Owner: swsystem
--

ALTER TABLE ONLY swsystem.sw_hashid_secret
    ADD CONSTRAINT sw_hashid_secret_pkey PRIMARY KEY (hash_project);


--
-- Name: idx_hist_jobs_completed; Type: INDEX; Schema: hist_cluster; Owner: swsystem
--

CREATE INDEX idx_hist_jobs_completed ON hist_cluster.hist_jobs USING btree (completed_at);


--
-- Name: idx_hist_jobs_stab_name; Type: INDEX; Schema: hist_cluster; Owner: swsystem
--

CREATE INDEX idx_hist_jobs_stab_name ON hist_cluster.hist_jobs USING btree (schema_name, table_name);


--
-- Name: idx_jp_contribution_log__func_ins_at; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_jp_contribution_log__func_ins_at ON swjackpot.jp_contribution_log USING btree (COALESCE(inserted_at, trx_date)) WITH (fillfactor='100');


--
-- Name: idx_jp_contribution_log_brand_id_trx_date_game_code; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_jp_contribution_log_brand_id_trx_date_game_code ON swjackpot.jp_contribution_log USING btree (brand_id, trx_date DESC, game_code) WITH (fillfactor='100');


--
-- Name: idx_jp_contribution_log_remote_trx_id; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_jp_contribution_log_remote_trx_id ON swjackpot.jp_contribution_log USING btree (remote_trx_id) WITH (fillfactor='100');


--
-- Name: idx_jp_contribution_log_trx_date_btree; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_jp_contribution_log_trx_date_btree ON swjackpot.jp_contribution_log USING btree (trx_date) WITH (fillfactor='100');


--
-- Name: idx_jp_contribution_log_trx_jp_id_pool_unique; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE UNIQUE INDEX idx_jp_contribution_log_trx_jp_id_pool_unique ON swjackpot.jp_contribution_log USING btree (trx_id, jackpot_id, pool) WITH (fillfactor='100');


--
-- Name: idx_jp_wallet_operation_log_ts; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_jp_wallet_operation_log_ts ON swjackpot.jp_wallet_operation_log USING btree (ts) WITH (fillfactor='100');


--
-- Name: idx_remote_jp_contribution_log__func_ins_at; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_remote_jp_contribution_log__func_ins_at ON swjackpot.remote_jp_contribution_log USING btree (COALESCE(inserted_at, trx_date));


--
-- Name: idx_remote_jp_contribution_log_brand_id_and_trx_date; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_remote_jp_contribution_log_brand_id_and_trx_date ON swjackpot.remote_jp_contribution_log USING btree (brand_id, trx_date DESC);


--
-- Name: idx_remote_jp_contribution_log_remote_trx_id; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_remote_jp_contribution_log_remote_trx_id ON swjackpot.remote_jp_contribution_log USING btree (remote_trx_id);


--
-- Name: idx_remote_jp_contribution_log_trx_date; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE INDEX idx_remote_jp_contribution_log_trx_date ON swjackpot.remote_jp_contribution_log USING btree (trx_date);


--
-- Name: idx_remote_jp_contribution_log_unique; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE UNIQUE INDEX idx_remote_jp_contribution_log_unique ON swjackpot.remote_jp_contribution_log USING btree (trx_id, jackpot_id, pool);


--
-- Name: jp_contribution_unique; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE UNIQUE INDEX jp_contribution_unique ON swjackpot.jp_contribution_log USING btree (trx_id, jackpot_id, pool);


--
-- Name: remote_jp_contribution_log_unique; Type: INDEX; Schema: swjackpot; Owner: swjackpot
--

CREATE UNIQUE INDEX remote_jp_contribution_log_unique ON swjackpot.remote_jp_contribution_log USING btree (trx_id, jackpot_id, pool);


--
-- Name: idx_audits_atype_eid_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_audits_atype_eid_ts ON swmanagement.audits USING btree (audits_summary_id, entity_id, ts DESC) WITH (fillfactor='100');


--
-- Name: idx_audits_eid_inname_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_audits_eid_inname_ts ON swmanagement.audits USING btree (entity_id, initiator_name, ts DESC) WITH (fillfactor='100');


--
-- Name: idx_audits_entity_id_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_audits_entity_id_ts ON swmanagement.audits USING btree (entity_id, ts DESC) WITH (fillfactor='100');


--
-- Name: idx_audits_inty_ts_atype_eid; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_audits_inty_ts_atype_eid ON swmanagement.audits USING btree (initiator_type, ts DESC, audits_summary_id, entity_id) WITH (fillfactor='100');


--
-- Name: idx_audits_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_audits_ts ON swmanagement.audits USING btree (ts) WITH (fillfactor='100');


--
-- Name: idx_games_grc_games_only; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_games_grc_games_only ON swmanagement.games USING btree ((((features ->> 'isGRCGame'::text))::boolean)) WHERE ((features ->> 'isGRCGame'::text))::boolean;


--
-- Name: idx_games_schema_definition_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_games_schema_definition_id ON swmanagement.games USING btree (schema_definition_id);


--
-- Name: idx_payments_brand_id_player_code_start_date; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_brand_id_player_code_start_date ON swmanagement.payments USING btree (brand_id, player_code, start_date DESC);


--
-- Name: idx_payments_created_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_created_at ON swmanagement.payments USING btree (created_at);


--
-- Name: idx_payments_dates_brand_id_nottest; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_dates_brand_id_nottest ON swmanagement.payments USING btree (start_date DESC, end_date, brand_id) WHERE (NOT is_test);


--
-- Name: idx_payments_dates_brand_id_test; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_dates_brand_id_test ON swmanagement.payments USING btree (start_date DESC, end_date, brand_id) WHERE is_test;


--
-- Name: idx_payments_ext_trx_id_brand_id_unique; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE UNIQUE INDEX idx_payments_ext_trx_id_brand_id_unique ON swmanagement.payments USING btree (ext_trx_id, brand_id);


--
-- Name: idx_payments_start_date_brand_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_start_date_brand_id ON swmanagement.payments USING btree (start_date DESC, brand_id);


--
-- Name: idx_payments_start_date_ordstat_bid; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_start_date_ordstat_bid ON swmanagement.payments USING btree (start_date DESC, order_status, brand_id);


--
-- Name: idx_payments_str_dt_bid_where_type; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_str_dt_bid_where_type ON swmanagement.payments USING btree (start_date DESC, brand_id) WHERE ((order_type)::text = ANY (ARRAY[('transfer_in'::character varying)::text, ('transfer_out'::character varying)::text]));


--
-- Name: idx_payments_updated_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_payments_updated_at ON swmanagement.payments USING btree (updated_at);


--
-- Name: idx_rounds_f_brand_id_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_brand_id_started_at ON swmanagement.rounds_finished USING btree (brand_id, started_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_brnd_id_finish_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_brnd_id_finish_at ON swmanagement.rounds_finished USING btree (brand_id, finished_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_brnd_ins_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_brnd_ins_at ON swmanagement.rounds_finished USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_fat_where_recovery; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_fat_where_recovery ON swmanagement.rounds_finished USING btree (finished_at) WITH (fillfactor='100') WHERE (recovery_type IS NOT NULL);


--
-- Name: idx_rounds_f_finish_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_finish_at ON swmanagement.rounds_finished USING btree (finished_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_finish_at_brnd_gc_pl; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_finish_at_brnd_gc_pl ON swmanagement.rounds_finished USING btree (finished_at DESC, brand_id, game_code, player_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_gc_brnd_start_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_gc_brnd_start_at ON swmanagement.rounds_finished USING btree (game_code, brand_id, started_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_inserted_at_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_inserted_at_started_at ON swmanagement.rounds_finished USING btree (COALESCE(inserted_at, started_at)) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_pl_brnd_finish_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_pl_brnd_finish_at ON swmanagement.rounds_finished USING btree (player_code, brand_id, finished_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_pl_brnd_start_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_pl_brnd_start_at ON swmanagement.rounds_finished USING btree (player_code, brand_id, started_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_pl_gc; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_pl_gc ON swmanagement.rounds_finished USING btree (player_code, game_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_start_at_brnd_pl_gc; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_start_at_brnd_pl_gc ON swmanagement.rounds_finished USING btree (started_at, brand_id, player_code, game_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_f_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_f_started_at ON swmanagement.rounds_finished USING btree (started_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_brand_id_finished_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_brand_id_finished_at ON swmanagement.rounds_history USING btree (brand_id, finished_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_brand_id_ins_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_brand_id_ins_at ON swmanagement.rounds_history USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_brand_id_ins_at_finished_false; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_brand_id_ins_at_finished_false ON swmanagement.rounds_history USING btree (brand_id, inserted_at DESC) WITH (fillfactor='100') WHERE (finished_at IS NULL);


--
-- Name: idx_rounds_history_finish_at_br_gc_pl; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_finish_at_br_gc_pl ON swmanagement.rounds_history USING btree (finished_at DESC, brand_id, game_code, player_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_finished_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_finished_at ON swmanagement.rounds_history USING btree (finished_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_gc_br_start_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_gc_br_start_at ON swmanagement.rounds_history USING btree (game_code, brand_id, started_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_inserted_at_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_inserted_at_started_at ON swmanagement.rounds_history USING btree (COALESCE(inserted_at, started_at)) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_pl_br_finish_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_pl_br_finish_at ON swmanagement.rounds_history USING btree (player_code, brand_id, finished_at DESC) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_pl_br_start_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_pl_br_start_at ON swmanagement.rounds_history USING btree (player_code, brand_id, started_at) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_player_game; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_player_game ON swmanagement.rounds_history USING btree (player_code, game_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_start_at_br_pl_gc; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_start_at_br_pl_gc ON swmanagement.rounds_history USING btree (started_at, brand_id, player_code, game_code) WITH (fillfactor='100');


--
-- Name: idx_rounds_history_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_rounds_history_started_at ON swmanagement.rounds_history USING btree (started_at) WITH (fillfactor='100');


--
-- Name: idx_sessions_history_inserted_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_sessions_history_inserted_at ON swmanagement.sessions_history USING btree (inserted_at) WITH (fillfactor='100');


--
-- Name: idx_sessions_history_started_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_sessions_history_started_at ON swmanagement.sessions_history USING btree (started_at) WITH (fillfactor='100');


--
-- Name: idx_spins_history_brand_player_game_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_spins_history_brand_player_game_ts ON swmanagement.spins_history USING btree (brand_id, player_code, game_code, ts DESC) WITH (fillfactor='100');


--
-- Name: idx_spins_history_hid_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_spins_history_hid_ts ON swmanagement.spins_history USING btree (COALESCE(spin_history_id, (1)::bigint), ts) WITH (fillfactor='100');


--
-- Name: idx_spins_history_inserted_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_spins_history_inserted_at ON swmanagement.spins_history USING btree (COALESCE(inserted_at, ts)) WITH (fillfactor='100');


--
-- Name: idx_spins_history_rid_ssn; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_spins_history_rid_ssn ON swmanagement.spins_history USING btree (round_id, spin_serial_number) WITH (fillfactor='100');


--
-- Name: idx_spins_history_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_spins_history_ts ON swmanagement.spins_history USING btree (ts) WITH (fillfactor='100');


--
-- Name: idx_wallet_entity_payment_log_inserted_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_entity_payment_log_inserted_at ON swmanagement.wallet_entity_payment_log USING btree (inserted_at);


--
-- Name: idx_wallet_entity_payment_log_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_entity_payment_log_ts ON swmanagement.wallet_entity_payment_log USING btree (ts);


--
-- Name: idx_wallet_entity_payment_log_type_tf; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_entity_payment_log_type_tf ON swmanagement.wallet_entity_payment_log USING btree (transfer_type, transfer_from);


--
-- Name: idx_wallet_entity_payment_log_type_tt; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_entity_payment_log_type_tt ON swmanagement.wallet_entity_payment_log USING btree (transfer_type, transfer_to);


--
-- Name: idx_wallet_operation_log_game_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_operation_log_game_id ON swmanagement.wallet_operation_log USING btree (game_id) WITH (fillfactor='100');


--
-- Name: idx_wallet_operation_log_public_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_operation_log_public_id ON swmanagement.wallet_operation_log USING btree (public_id) WITH (fillfactor='100');


--
-- Name: idx_wallet_operation_log_ts; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_operation_log_ts ON swmanagement.wallet_operation_log USING btree (ts) WITH (fillfactor='100');


--
-- Name: idx_wallet_win_bet_brand_id_payment_date_currency; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_win_bet_brand_id_payment_date_currency ON swmanagement.wallet_win_bet USING btree (brand_id, payment_date, currency) WITH (fillfactor='100');


--
-- Name: idx_wallet_win_bet_game_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_win_bet_game_id ON swmanagement.wallet_win_bet USING btree (game_id) WITH (fillfactor='100');


--
-- Name: idx_wallet_win_bet_inserted_at; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_win_bet_inserted_at ON swmanagement.wallet_win_bet USING btree (COALESCE(inserted_at, payment_date)) WITH (fillfactor='100');


--
-- Name: idx_wallet_win_bet_paydate; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_win_bet_paydate ON swmanagement.wallet_win_bet USING btree (payment_date) WITH (fillfactor='100');


--
-- Name: idx_wallet_win_bet_trx_id; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX idx_wallet_win_bet_trx_id ON swmanagement.wallet_win_bet USING btree (trx_id) WITH (fillfactor='100');


--
-- Name: wallet_win_bet_brand_id_payment_date_currency; Type: INDEX; Schema: swmanagement; Owner: swmanagement
--

CREATE INDEX wallet_win_bet_brand_id_payment_date_currency ON swmanagement.wallet_win_bet USING btree (brand_id, payment_date, currency);


--
-- Name: pathman_config allow_select; Type: POLICY; Schema: public; Owner: swsystem
--

CREATE POLICY allow_select ON public.pathman_config FOR SELECT USING (true);


--
-- Name: pathman_config_params allow_select; Type: POLICY; Schema: public; Owner: swsystem
--

CREATE POLICY allow_select ON public.pathman_config_params FOR SELECT USING (true);


--
-- Name: pathman_config deny_modification; Type: POLICY; Schema: public; Owner: swsystem
--

CREATE POLICY deny_modification ON public.pathman_config USING (public.check_security_policy(partrel));


--
-- Name: pathman_config_params deny_modification; Type: POLICY; Schema: public; Owner: swsystem
--

CREATE POLICY deny_modification ON public.pathman_config_params USING (public.check_security_policy(partrel));


--
-- Name: pathman_config; Type: ROW SECURITY; Schema: public; Owner: swsystem
--

ALTER TABLE public.pathman_config ENABLE ROW LEVEL SECURITY;

--
-- Name: pathman_config_params; Type: ROW SECURITY; Schema: public; Owner: swsystem
--

ALTER TABLE public.pathman_config_params ENABLE ROW LEVEL SECURITY;

--
-- Name: SCHEMA pglogical; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA pglogical TO PUBLIC;


--
-- Name: SCHEMA swmanagement; Type: ACL; Schema: -; Owner: swmanagement
--

GRANT USAGE ON SCHEMA swmanagement TO redis_game_offloader;
GRANT USAGE ON SCHEMA swmanagement TO kafka_offloader;


--
-- Name: FUNCTION fnc_hist_run_background_jobs(); Type: ACL; Schema: hist_cluster; Owner: swsystem
--

REVOKE ALL ON FUNCTION hist_cluster.fnc_hist_run_background_jobs() FROM PUBLIC;


--
-- Name: FUNCTION fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying); Type: ACL; Schema: public; Owner: swsystem
--

GRANT ALL ON FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_get_random_tblspcs(p_number integer, p_exclude_tblspc character varying) TO swjackpot;


--
-- Name: FUNCTION fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: swsystem
--

GRANT ALL ON FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: swsystem
--

GRANT ALL ON FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_transfer_finish_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: swsystem
--

GRANT ALL ON FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_transfer_prepare_table(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying); Type: ACL; Schema: public; Owner: swsystem
--

GRANT ALL ON FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swmanagement;
GRANT ALL ON FUNCTION public.special_fnc_zfs_change_storage(p_schema_name character varying, p_table_name character varying) TO swjackpot;


--
-- Name: FUNCTION get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer); Type: ACL; Schema: swsystem; Owner: swsystem
--

REVOKE ALL ON FUNCTION swsystem.get_sw_hashid(p_hash_project character varying, OUT po_hash_salt text, OUT po_hash_length integer) FROM PUBLIC;


--
-- Name: TABLE pathman_allowed_tspaces; Type: ACL; Schema: public; Owner: postgres
--

GRANT SELECT ON TABLE public.pathman_allowed_tspaces TO PUBLIC;


--
-- Name: TABLE audits; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.audits TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.audits TO kafka_offloader;


--
-- Name: SEQUENCE audits_audit_id_seq; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT USAGE ON SEQUENCE swmanagement.audits_audit_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.audits_audit_id_seq TO kafka_offloader;


--
-- Name: SEQUENCE payments_id_seq; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT USAGE ON SEQUENCE swmanagement.payments_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.payments_id_seq TO kafka_offloader;


--
-- Name: TABLE rounds_finished; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.rounds_finished TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.rounds_finished TO kafka_offloader;


--
-- Name: TABLE sessions_history; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.sessions_history TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.sessions_history TO kafka_offloader;


--
-- Name: SEQUENCE spinhistories_seq; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT USAGE ON SEQUENCE swmanagement.spinhistories_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.spinhistories_seq TO kafka_offloader;


--
-- Name: TABLE spins_history; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.spins_history TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.spins_history TO kafka_offloader;


--
-- Name: SEQUENCE wallet_entity_payment_log_id_seq; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT USAGE ON SEQUENCE swmanagement.wallet_entity_payment_log_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_entity_payment_log_id_seq TO kafka_offloader;


--
-- Name: TABLE wallet_entity_payment_log; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_entity_payment_log TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_entity_payment_log TO kafka_offloader;


--
-- Name: TABLE wallet_operation_log; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_operation_log TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_operation_log TO kafka_offloader;


--
-- Name: TABLE wallet_win_bet; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_win_bet TO redis_game_offloader;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swmanagement.wallet_win_bet TO kafka_offloader;


--
-- Name: SEQUENCE wallet_win_bet_id_seq; Type: ACL; Schema: swmanagement; Owner: swmanagement
--

GRANT USAGE ON SEQUENCE swmanagement.wallet_win_bet_id_seq TO redis_game_offloader;
GRANT USAGE ON SEQUENCE swmanagement.wallet_win_bet_id_seq TO kafka_offloader;


--
-- PostgreSQL database dump complete
--

