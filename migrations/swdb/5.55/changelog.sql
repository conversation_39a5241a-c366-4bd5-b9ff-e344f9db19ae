--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset vladimir.minakov:2025-01-22-SWS-51270-create-database-migration-to-add-new-columns-and-tables-for-enhanced-domains
--comment Add description, provider, status, expiryDate, and type fields to domain models
SET search_path = swmanagement;

-- Create enum for domain status
CREATE TYPE enum_domain_status AS ENUM ('active', 'suspended');

-- Create enum for static domain type
CREATE TYPE enum_static_domain_type AS ENUM ('static', 'lobby', 'live streaming', 'ehub');

ALTER TABLE dynamic_domains
    ADD COLUMN description TEXT,
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE static_domains
    ADD COLUMN description TEXT,
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE,
    ADD COLUMN type enum_static_domain_type NOT NULL DEFAULT 'static';

ALTER TABLE lobby_domains
    ADD COLUMN description TEXT,
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'Active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE;

RESET search_path;

--rollback SET search_path = swmanagement;

--rollback ALTER TABLE dynamic_domains DROP COLUMN description;
--rollback ALTER TABLE dynamic_domains DROP COLUMN provider;
--rollback ALTER TABLE dynamic_domains DROP COLUMN status;
--rollback ALTER TABLE dynamic_domains DROP COLUMN expiry_date;

--rollback ALTER TABLE static_domains DROP COLUMN description;
--rollback ALTER TABLE static_domains DROP COLUMN provider;
--rollback ALTER TABLE static_domains DROP COLUMN status;
--rollback ALTER TABLE static_domains DROP COLUMN expiry_date;
--rollback ALTER TABLE static_domains DROP COLUMN type;

--rollback ALTER TABLE lobby_domains DROP COLUMN description;
--rollback ALTER TABLE lobby_domains DROP COLUMN provider;
--rollback ALTER TABLE lobby_domains DROP COLUMN status;
--rollback ALTER TABLE lobby_domains DROP COLUMN expiry_date;

--rollback DROP TYPE IF EXISTS enum_domain_status;
--rollback DROP TYPE IF EXISTS enum_static_domain_type;

--rollback RESET search_path;
