{"swagger": "2.0", "info": {"description": "Skywind - Play API for Game Provider", "version": "5.55", "title": "Skywind - Play API for Game Provider"}, "basePath": "/v2", "schemes": ["http", "https"], "produces": ["application/json"], "securityDefinitions": {"provider_code": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-provider-code", "in": "header"}, "provider_secret": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-provider-secret", "in": "header"}}, "paths": {"/play/startgame": {"post": {"security": [{"provider_code": []}, {"provider_secret": []}], "tags": ["Play"], "summary": "Authenticates game server to play game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request", "required": true, "schema": {"required": ["startGameToken"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDA..."}}}}], "responses": {"200": {"description": "The gameserver has been authenticated", "schema": {"required": ["gameToken", "balance"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "balance": {"$ref": "#/definitions/Balance"}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 240: Game not found\n- 320: Start game token error\n- 321: Start game token is expired\n- 703: IP address cannot be resolved\n- 690: Bonus coins not available\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 730: <PERSON><PERSON><PERSON> has different environment id\n- 735: <PERSON><PERSON>ty is under maintenance\n- 751: Refer<PERSON> is missing\n"}, "403": {"description": "- 701: Country of IP is restricted\n- 1500: Can't execute operation. Player is on timeout.\n- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON>cy not found\n- 102: Player not found\n- 104: Limits for currency not found\n"}}}}, "/play/fun/startgame": {"post": {"security": [{"provider_code": []}, {"provider_secret": []}], "tags": ["Play"], "summary": "Authenticates game server to play fun game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request. Accepts jwt-token or object", "required": true, "schema": {"required": ["startGameToken"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDAxIiwicHJvdmlkZXJDb2RlIjoicHJvdmlkZXJDb2RlMSIsInByb3ZpZGVyR2FtZUNvZGUiOiJHQU1FMDAxIiwicGxheWVyQ29kZSI6MjUwMDEsImN1cnJlbmN5IjoiVVNEIn0.q5-2cnZN1xmXwULz0JlLdxEp7lGL40KWd_XroaWk5WY"}}}}], "responses": {"200": {"description": "The gameserver has been authenticated", "schema": {"required": ["gameToken", "balance"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "balance": {"$ref": "#/definitions/Balance"}, "player": {"$ref": "#/definitions/PlayerInfo"}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 240: Game not found\n- 320: Start game token error\n- 321: Start game token is expired\n- 703: IP address cannot be resolved\n- 712: Player is suspended\n- 730: Entity has different environment id\n- 735: Entity is under maintenance"}, "403": {"description": "- 701: Country of IP is restricted\n- 1500: Can't execute operation. Player is on timeout.\n- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON>cy not found\n- 102: Player not found\n- 104: Limits for currency not found\n"}}}}, "/play/balance": {"get": {"tags": ["Play"], "summary": "Gets player's balance with game stats", "parameters": [{"$ref": "#/parameters/gameToken"}], "responses": {"200": {"description": "The player's balance with game stats", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/balances": {"get": {"tags": ["Play"], "summary": "Gets all player's balances with game stats", "parameters": [{"$ref": "#/parameters/gameToken"}], "responses": {"200": {"description": "All players balances with game stats", "schema": {"$ref": "#/definitions/Balances"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment": {"put": {"tags": ["Play"], "summary": "Commits game payment operation", "parameters": [{"name": "paymentRequest", "in": "body", "description": "Payment (credit, debit) for one of attribute key [\"balance\", \"xp\", \"level\", ...]", "required": true, "schema": {"$ref": "#/definitions/GamePaymentOperation"}}], "responses": {"200": {"description": "The player's payment operation has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 675: Invalid or not permitted attribute\n- 676: Invalid payment action\n- 677: Negative action amount\n- 678: Payment action list is empty\n- 679: Operation for merchants support maximum 1 operation for bet and 1 for win\n- 91: Player does not have sufficient balance to perform an operation\n- 729: Entity does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment/rollback": {"put": {"tags": ["Play"], "summary": "Rollbacks player's transaction", "parameters": [{"name": "rollbackBetRequest", "in": "body", "description": "Rollback transaction", "required": true, "schema": {"$ref": "#/definitions/RollbackBetRequest"}}], "responses": {"200": {"description": "The player's payment has been rolled back", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 672: Could not find transaction to perform operation.\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}, "500": {"description": "- 673: Not yet ready to rollback transaction. Repeat later."}}}}, "/play/transfer": {"put": {"tags": ["Play"], "summary": "Transfer player's balance", "parameters": [{"name": "transferRequest", "in": "body", "description": "Transfer in/out", "required": true, "schema": {"$ref": "#/definitions/TransferRequest"}}], "responses": {"200": {"description": "The player's transfer has been done", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "500": {"description": "- 668: Transaction is processing\n"}}}}, "/play/payment/transactionId": {"post": {"tags": ["Play"], "summary": "Generates payment transaction identifier for further use in commit payment operation.", "parameters": [{"name": "transactionIdRequest", "in": "body", "description": "TransactionId Request", "required": true, "schema": {"required": ["gameToken"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}}}}], "responses": {"200": {"description": "Response with transactionId that can be used for further payment operation", "schema": {"required": ["transactionId"], "properties": {"transactionId": {"type": "string", "description": "Unique transaction identifier", "example": "ASDJFKJQ2L3I48DFASJDFADSFJ434"}}}}, "400": {"description": "- 40: Validation error\n- 320: Start game token error\n- 321: Start game token is expired"}}}}, "/play/freebet": {"post": {"tags": ["Play"], "summary": "Get free bet info", "parameters": [{"name": "freeBetInfoRequest", "in": "body", "description": "Free bet info request", "required": true, "schema": {"required": ["gameToken", "coinMultiplier", "stakeAll"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "coinMultiplier": {"type": "number", "description": "The coin multiplier to get total free bet value (e.g. number of lines in slot games)", "example": 25}, "stakeAll": {"type": "array", "items": {"type": "number"}, "description": "Possible coin values", "example": [0.1, 0.2, 0.5, 1, 2]}, "skipCoinValidation": {"type": "boolean", "description": "Do not throw error when promo's coin is not in stakeAll array", "example": true}}}}], "responses": {"200": {"description": "Response with free bet info", "schema": {"required": ["amount", "coin"], "properties": {"amount": {"type": "number", "description": "Count of available free bets", "example": 10}, "coin": {"type": "number", "description": "Free bet coin per line", "example": 0.1}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 685: Insufficient free bets balance\n- 686: Invalid free bet\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/regulatory-action": {"post": {"tags": ["Play"], "summary": "Perform regulatory action over player", "parameters": [{"name": "performRegulatoryActionRequest", "in": "body", "description": "Regulatory action request", "required": true, "schema": {"required": ["gameToken", "action"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "action": {"type": "array", "items": {"type": "string"}, "description": "Possible regulatory actions to perform over player: resetRealityCheck, closeSession, cancelBet", "example": ["closeSession", "resetReality<PERSON><PERSON>ck"]}, "params": {"type": "object", "example": {"regulation": "uk"}}}}}], "responses": {"204": {"description": "Regulatory action was performed"}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/play/bonusCoins": {"post": {"tags": ["Play"], "summary": "Redeem bonus coins", "parameters": [{"name": "bonusCoinsRedeemRequest", "in": "body", "description": "Bonus coins redeem request", "required": true, "schema": {"required": ["gameToken", "transactionId", "roundId", "promoId", "amount"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "extTransactionId": {"type": "string", "description": "external transaction id"}, "roundId": {"type": "number", "description": "round id"}, "promoId": {"type": "string", "description": "promo id"}, "amount": {"type": "number", "description": "Redeem balance amount", "example": 72.5}}}}], "responses": {"200": {"description": "The redeem bonus coins has been done", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 674: Malformed JSON\n- 691: Insufficient bonus coins balance\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}, "500": {"description": "- 668: Transaction is processing\n"}}}}, "/play/payment/transfer/in": {"put": {"tags": ["Play"], "summary": "Transfer money from internal wallet to game provider", "parameters": [{"name": "externalTransferIn", "in": "body", "description": "Transfer in request", "required": true, "schema": {"$ref": "#/definitions/ExternalTransferInRequest"}}], "responses": {"200": {"description": "Money from internal wallet has been transfered", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 90: Amount is negative\n- 729: Entity does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/play/payment/transfer/out": {"put": {"tags": ["Play"], "summary": "Return money to internal wallet from game provider", "parameters": [{"name": "externalTransferOut", "in": "body", "description": "Transfer out request", "required": true, "schema": {"$ref": "#/definitions/ExternalTransferOutRequest"}}], "responses": {"200": {"description": "Money to internal wallet has been transfered", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 90: Amount is negative\n- 92: Bad TransactionId\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}, "/play/game/finalize": {"post": {"tags": ["Play"], "summary": "Finalize game", "parameters": [{"name": "finalizeGameRequest", "in": "body", "description": "Finalize game: log wallet operation and send request to operator to resolve round(optional)", "required": true, "schema": {"$ref": "#/definitions/FinalizeGameRequest"}}], "responses": {"201": {"description": "Accepted"}, "400": {"description": "- 92: Bad TransactionId\n- 730 Entity has different environment id\n- 506 Merchant internal error\n- 322 GameTokenError", "schema": {"$ref": "#/definitions/Error"}}}}}, "/play/payment/deferred": {"put": {"tags": ["Play"], "summary": "Commit / cancel deferred payment", "parameters": [{"name": "deferredPaymentRequest", "in": "body", "description": "Deferred payment request", "required": true, "schema": {"$ref": "#/definitions/DeferredPaymentOperation"}}], "responses": {"200": {"description": "Deferred payment operation has been committed", "schema": {"$ref": "#/definitions/Balance"}}, "201": {"description": "Deferred payment canceled successfully"}, "400": {"description": "- 675: Invalid or not permitted attribute\n- 676: Invalid payment action\n- 677: Negative action amount\n- 678: Payment action list is empty\n- 679: Operation for merchants support maximum 1 operation for bet and 1 for win\n- 91: Player does not have sufficient balance to perform an operation\n- 729: Entity does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 1526: Deferred payment processing error", "schema": {"$ref": "#/definitions/Error"}}}}}}, "definitions": {"GameToken": {"type": "string", "description": "Game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzY290Y2guaW8iLCJleHAiOjEzMDA4MTkzO..."}, "GamePaymentOperation": {"type": "object", "description": "payment information", "required": ["gameToken", "transactionId", "roundId", "actions"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "round id"}, "eventId": {"type": "number", "description": "Event id"}, "roundEnded": {"type": "boolean", "description": "true|false - if game round is ended or not"}, "deviceId": {"type": "string", "description": "device id"}, "extTransactionId": {"type": "string", "description": "external transaction id"}, "actions": {"type": "array", "description": "list of payment actions", "items": {"$ref": "#/definitions/GamePaymentAction"}}}}, "TransferRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "string", "description": "Game round id"}, "operation": {"type": "string", "description": "transfer-in | transfer-out"}, "amount": {"type": "number", "description": "Amount of transfer"}}}, "RollbackBetRequest": {"type": "object", "description": "Rollback transaction info", "required": ["gameToken", "extTransactionId", "originalTransactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "extTransactionId": {"type": "string", "description": "Transaction id of provider's transaction"}, "originalTransactionId": {"type": "string", "description": "Transaction id of original transaction to rollback"}, "roundId": {"type": "number", "description": "round id"}}}, "PlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "status": {"type": "string", "description": "player status", "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code", "example": "US"}, "currency": {"type": "string", "description": "currency code", "example": "USD"}, "language": {"type": "string", "description": "language code", "example": "EN"}, "gameGroup": {"type": "string", "description": "player's game group", "example": "VIP"}}}, "Balance": {"type": "object", "required": ["main"], "properties": {"main": {"type": "number", "description": "balance", "example": 1000}, "externalBalances": {"$ref": "#/definitions/ExtraBalances"}}}, "Balances": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Balance"}, "example": {"USD": {"main": 1000, "stats": {"xp": 10, "level": 5, "stars": 100}}, "EUR": {"main": 2000, "stats": {"stars": 20}}}}, "ExtraBalances": {"type": "object", "properties": {"xp": {"type": "number", "description": "xp", "example": 567}, "level": {"type": "number", "description": "level", "example": 5}, "stars": {"type": "number", "description": "stars", "example": 10}}}, "GamePaymentAction": {"type": "object", "description": "payment action", "required": ["action", "attribute", "amount"], "properties": {"action": {"type": "string", "description": "one of [\"credit\" | \"debit\"]", "example": "credit", "enum": ["credit", "debit"]}, "attribute": {"type": "string", "description": "one of [\"balance\" | \"xp\" | \"level\" | \"stars\" | \"diamonds\"]", "example": "xp", "enum": ["balance", "xp", "level", "stars", "diamonds"]}, "amount": {"type": "number", "description": "positive amount", "example": 10}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 677}, "message": {"type": "string", "description": "error message", "example": "Negative transaction operation value"}}}, "ExternalTransferInRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "amount": {"type": "number", "description": "Amount of transfer"}, "deviceId": {"type": "string", "description": "Platform type web or mobile"}}}, "ExternalTransferOutRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "amount": {"type": "number", "description": "Amount of transfer"}, "deviceId": {"type": "string", "description": "Platform type web or mobile"}, "actualBetAmount": {"type": "number", "description": "Aggregated summ of all bets in game between last transfer/in and current transfer/out"}, "actualWinAmount": {"type": "number", "description": "Aggregated summ of all wins in game between last transfer/in and current transfer/out"}}}, "RoundStatistics": {"type": "object", "properties": {"totalBet": {"type": "number", "description": "Total bet amount that was done for round", "example": 10}, "totalWin": {"type": "string", "description": "Total win amount that was done for round", "example": 100}, "totalJpContribution": {"type": "string", "description": "Total jackpot contribution amount in round", "example": 0.001}, "totalJPWin": {"type": "string", "description": "Total jackpot win amount", "example": 11.1}}}, "FinalizeGameRequest": {"type": "object", "description": "payment information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "round id"}, "roundPID": {"type": "string", "description": "round public id"}, "roundStatistics": {"$ref": "#/definitions/RoundStatistics"}, "finalizationType": {"type": "string", "enum": ["notSupported", "roundStatistics", "offlinePayments", "forceFinish", "manualPayments"], "example": "forceFinish", "description": "finalization type"}}}, "DeferredPaymentOperation": {"allOf": [{"$ref": "#/definitions/GamePaymentOperation"}, {"type": "object", "required": ["deferredPayment"], "properties": {"cancel": {"type": "boolean", "description": "true if we need to cancel payment", "example": false}, "deferredPayment": {"type": "object", "description": "deferred payment", "properties": {"id": {"type": "string", "description": "deferred payment id", "example": "24950"}}, "required": ["id"]}}}]}}, "parameters": {"providerGameCode": {"name": "providerGameCode", "in": "path", "description": "Provider game code", "required": true, "type": "string"}, "gameToken": {"name": "gameToken", "in": "query", "description": "Game token", "required": true, "type": "string"}, "ghAppToken": {"name": "token", "in": "query", "description": "Token generated for game history app", "required": true, "type": "string"}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "roundId": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundId__gt": {"in": "query", "name": "roundId__gt", "description": "roundId is greater-than", "required": false, "type": "string"}, "roundId__gte": {"in": "query", "name": "roundId__gte", "description": "roundId is greater-than-or-equal", "required": false, "type": "string"}, "roundId__lt": {"in": "query", "name": "roundId__lt", "description": "roundId is less-than", "required": false, "type": "string"}, "roundId__lte": {"in": "query", "name": "roundId__lte", "description": "roundId is less-than-or-equal", "required": false, "type": "string"}, "ts": {"name": "ts", "in": "query", "description": "Timestamp (ISO 8601 timestamp)", "required": false, "type": "string"}}}