{"swagger": "2.0", "info": {"description": "Skywind - Critical Files - API", "version": "5.55", "title": "Skywind - Critical Files - API"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"critical-files": "Permission to Critical Files API"}}}, "paths": {"/critical-files/games/info": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get list of critical files and their hashes", "parameters": [{"$ref": "#/parameters/getGameCriticalFilesInfoRequest"}], "responses": {"200": {"description": "Critical files list and their hashes", "schema": {"$ref": "#/definitions/GameCriticalFilesList"}}, "400": {"description": "- 40: Validation error"}}}}, "/entities/{path}/critical-files/games/info": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get list of critical files and their hashes by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/getGameCriticalFilesInfoRequest"}], "responses": {"200": {"description": "Critical files list and their hashes", "schema": {"$ref": "#/definitions/GameCriticalFilesList"}}, "400": {"description": "- 40: Validation error"}}}}, "/critical-files/platform/info": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get list of critical files and their hashes", "parameters": [{"$ref": "#/parameters/getPlatformCriticalFilesInfoRequest"}], "responses": {"200": {"description": "Critical files list and their hashes", "schema": {"$ref": "#/definitions/PlatformCriticalFilesList"}}, "400": {"description": "- 40: Validation error"}}}}, "/entities/{path}/critical-files/platform/info": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get list of critical files and their hashes by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/getPlatformCriticalFilesInfoRequest"}], "responses": {"200": {"description": "Critical files list and their hashes", "schema": {"$ref": "#/definitions/PlatformCriticalFilesList"}}, "400": {"description": "- 40: Validation error"}}}}, "/critical-files/versions": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get versions of critical modules", "parameters": [{"$ref": "#/parameters/getGameCriticalFilesVersionsRequest"}], "responses": {"200": {"description": "Critical modules names with versions", "schema": {"$ref": "#/definitions/CriticalFilesVersions"}}, "400": {"description": "- 40: Validation error"}}}}, "/entities/{path}/critical-files/versions": {"post": {"security": [{"apiKey": []}, {"Permissions": ["critical-files"]}], "tags": ["Critical Files API"], "summary": "Get versions of critical modules by path", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/getGameCriticalFilesVersionsRequest"}], "responses": {"200": {"description": "Critical modules names with versions", "schema": {"$ref": "#/definitions/CriticalFilesVersions"}}, "400": {"description": "- 40: Validation error"}}}}}, "definitions": {"GameCriticalFilesList": {"type": "object", "description": "Info", "example": {"games": [{"code": "sw_mrmnky", "list": [{"lib/src/skywind/criticalFile1.js": "1F1AE2BE9DD2E98D63E"}, {"lib/src/skywind/criticalFile2.js": "51B4EAC98AF84251C4E"}]}, {"code": "sw_888t", "list": [{"lib/src/skywind/criticalFile1.js": "6486FACD330A803B69B"}, {"lib/src/skywind/criticalFile2.js": "ACC31E5247C9EFBEF78"}]}]}}, "PlatformCriticalFilesList": {"type": "object", "description": "Info", "example": {"modules": [{"name": "sw-random-cs", "list": [{"src/skywind/platformCriticalFile1.js": "1F1AE2BE9DD2E98D63E"}, {"src/skywind/platformCriticalFile2.js": "51B4EAC98AF84251C4E"}]}]}}, "CriticalFilesVersions": {"type": "object", "description": "Info", "example": [{"gamecCode": "sw-random-cs", "version": "1.0.5"}]}}, "parameters": {"getGameCriticalFilesInfoRequest": {"name": "getGameCriticalFilesInfoRequest", "in": "body", "description": "Game Critical files info request", "required": true, "schema": {"properties": {"regulation": {"type": "string", "example": "italian", "enum": ["italian", "spanish", "greek", "lithuanian", "swiss", "brazilian"], "description": "Regulation type"}, "games": {"type": "array", "items": {"type": "string", "example": "sw_mrmnky"}}, "includeVersions": {"type": "boolean", "example": false}}}}, "getPlatformCriticalFilesInfoRequest": {"name": "getPlatformCriticalFilesInfoRequest", "in": "body", "description": "Platform Critical files info request", "required": true, "schema": {"properties": {"regulation": {"type": "string", "example": "italian", "enum": ["italian", "spanish", "greek", "lithuanian", "swiss", "brazilian"], "description": "Regulation type"}}}}, "getGameCriticalFilesVersionsRequest": {"name": "getGameCriticalFilesVersionsRequest", "in": "body", "description": "Game Critical files info request", "required": true, "schema": {"properties": {"regulation": {"type": "string", "example": "italian", "enum": ["italian", "spanish", "greek", "lithuanian", "swiss", "brazilian"], "description": "Regulation type"}, "modules": {"type": "array", "items": {"type": "string", "example": "sw_mrmnky"}}}}}, "path": {"name": "path", "in": "path", "description": "Business entity path", "required": true, "type": "string"}}}