{"swagger": "2.0", "info": {"description": "Skywind - API for Terminal", "version": "5.55", "title": "Skywind - API for Terminal"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-terminal-token", "in": "header"}}, "paths": {"/terminals/lobbies": {"get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "parameters": [{"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "summary": "Gets list of lobbies under terminal", "description": "Gets list of lobbies for a entity. Header should contain \"x-terminal-token\" with brandId", "responses": {"200": {"description": "List of lobbies", "schema": {"type": "array", "items": {"$ref": "#/definitions/LobbyData"}}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/terminals/lobbies/{lobbyId}": {"parameters": [{"$ref": "#/parameters/lobbyId"}, {"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "summary": "Gets lobby by public id", "description": "Gets lobby extended info by public id. Header should contain \"x-terminal-token\" with brandId", "responses": {"200": {"description": "Lobbies extended info", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/terminals/lobby-info/{lobbyId}": {"parameters": [{"$ref": "#/parameters/lobbyId"}, {"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "summary": "Gets lobby by public id (alternative path)", "description": "Alternative path for getting lobby extended info by public id. Header should contain \"x-terminal-token\" with brandId", "responses": {"200": {"description": "Lobbies extended info", "schema": {"$ref": "#/definitions/LobbyData"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/terminals/lobbies/{lobbyId}/theme": {"parameters": [{"$ref": "#/parameters/lobbyId"}], "get": {"tags": ["Lobby"], "produces": ["text/css", "application/json"], "summary": "Gets lobby css by public id", "description": "", "responses": {"200": {"description": "Lobby styles", "schema": {"type": "string", "example": "{}"}}}}}, "/terminals/lobbies/{lobbyId}/image/{image}": {"parameters": [{"$ref": "#/parameters/lobbyId"}, {"$ref": "#/parameters/image"}], "get": {"tags": ["Lobby"], "produces": ["image/x-icon", "image/png", "application/json"], "summary": "Get an image by public id", "description": "Returns the image in the requested format if available", "responses": {"200": {"description": "The requested image", "schema": {"type": "string", "format": "binary"}}}}}, "/terminals/lobbies/{lobbyId}/options": {"parameters": [{"$ref": "#/parameters/lobbyId"}], "get": {"tags": ["Lobby"], "summary": "Gets lobby options by public id", "description": "Gets lobby options by public id. Can be accessed with or without terminal token.", "responses": {"200": {"description": "Lobby options", "schema": {"type": "object"}}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n- 51: Could not find entity\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/terminals/lobbies/{lobbyId}/players/external/login": {"parameters": [{"$ref": "#/parameters/lobbyId"}], "post": {"tags": ["Lobby"], "summary": "Logs external player in using lobby ID", "description": "Logs merchant's player in by its code for terminal using lobby ID. Player defined by ticket.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LoginExternalPlayerDataInfo"}}], "responses": {"200": {"description": "Login information with lobby ID", "schema": {"type": "object", "properties": {"token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "lobbyId": {"type": "string", "description": "Lobby ID", "example": 123}}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}, "500": {"description": "- 505: Merchant should have url and password in params\n- 505: Merchant internal error\n"}}}}, "/terminals/lobbies/{lobbyId}/players/login": {"parameters": [{"$ref": "#/parameters/lobbyId"}], "post": {"tags": ["Lobby"], "summary": "Logs player in using lobby ID", "description": "Logs player in for terminal using lobby ID. Can be accessed with or without terminal token.", "parameters": [{"in": "body", "name": "info", "required": true, "description": "Login data. Should contains player's code and password.", "schema": {"$ref": "#/definitions/LoginPlayerDataInfo"}}], "responses": {"200": {"description": "Login information with lobby ID", "schema": {"type": "object", "allOf": [{"$ref": "#/definitions/LoginPlayerInfo"}], "properties": {"lobbyId": {"type": "string", "description": "Lobby ID", "example": 123}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 703: IP address cannot be resolved\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 860: Player has another session", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 816: Player or Password does not match\n- 230: Player authentication is blocked\n- 813: <PERSON><PERSON> does not match"}, "403": {"description": "- 701: Country of IP is restricted\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 324: <PERSON><PERSON> is not found\n"}}}}, "/terminals/lobbies/{lobbyId}/refresh-captcha/{username}": {"parameters": [{"$ref": "#/parameters/lobbyId"}, {"name": "username", "in": "path", "type": "string", "description": "User name", "required": true}], "get": {"tags": ["Lobby"], "summary": "Refresh Captcha for a specific lobby", "description": "Gets new captcha for a specific lobby. Can be accessed with or without terminal token.", "responses": {"200": {"description": "<PERSON><PERSON>", "schema": {"$ref": "#/definitions/Captcha"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n- 51: Could not find entity\n"}}}}, "/terminals/players/login": {"post": {"tags": ["Authentication"], "security": [{"apiKey": []}], "summary": "Logs player in under terminal", "description": "Logs player in for terminal. Header should contain \"x-terminal-token\" with brandId.", "parameters": [{"in": "body", "name": "info", "required": true, "description": "Login data. Should contains player's code and password.", "schema": {"$ref": "#/definitions/LoginPlayerDataInfo"}}, {"name": "ip", "in": "query", "required": false, "type": "string", "description": "Initiator ip adress"}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginPlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 703: IP address cannot be resolved\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 860: Player has another session", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n- 816: Player or Password does not match\n- 230: Player authentication is blocked\n- 813: <PERSON><PERSON> does not match"}, "403": {"description": "- 701: Country of IP is restricted\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/terminals/players/external/login": {"post": {"tags": ["Authentication"], "security": [{"apiKey": []}], "summary": "Logs external player in under terminal", "description": "Logs merchant's player in by its code for terminal. Player defined by ticket. Header should contain \"x-terminal-token\" with brandId.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/LoginExternalPlayerDataInfo"}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginExternalPlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}, "500": {"description": "- 505: Merchant should have url and password in params\n- 505: Merchant internal error\n"}}}}, "/terminals/create": {"post": {"tags": ["Terminal"], "security": [{"apiKey": []}], "deprecated": true, "summary": "Creates terminal for entity under terminal", "description": "Method creates new terminal for a entity by title and lobby id and returns its info with public id. Header should contain \"x-terminal-token\" with brandId.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/TerminalCreateData"}}], "responses": {"201": {"description": "Created terminal", "schema": {"$ref": "#/definitions/TerminalData"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n- 208: Terminal token error\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 324: <PERSON><PERSON> is not found\n"}, "409": {"description": "- 326: Terminal already exists\n"}}}}, "/terminals/info/{terminalId}": {"parameters": [{"$ref": "#/parameters/terminalId"}], "get": {"tags": ["Terminal"], "security": [{"apiKey": []}], "deprecated": true, "summary": "Gets terminal info", "description": "Method gets terminal information by its public id. Header should contain \"x-terminal-token\" with brandId.", "responses": {"200": {"description": "Terminal info", "schema": {"$ref": "#/definitions/TerminalData"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n"}, "404": {"description": "- 327: Terminal is not found\n"}}}}, "/terminals/games": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets list of games", "description": "Get list of games available for a entity and filters it by parameters. Case sortBy == categoryList returns games from included list of category before others. Header should contain \"x-terminal-token\" with brandId", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameCategoryId__in"}, {"$ref": "#/parameters/onlyCategoryGames"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/gameProviderCodeEquality"}, {"$ref": "#/parameters/gameProviderCode__in"}, {"$ref": "#/parameters/gameProviderTitleEquality"}, {"$ref": "#/parameters/gameProviderTitleContains"}, {"$ref": "#/parameters/gameProviderTitleNotContains"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/limitsCurrency"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 400: Pagination offset is not valid\n- 401: Pagination limit is not valid\n- 402: Filter is not valid\n- 403: Key is not valid for sort by\n- 404: Sort order is not valid\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/terminals/fun/games/{gameCode}": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets fun game URL", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}, {"$ref": "#/parameters/languageInQuery"}], "responses": {"200": {"description": "Game URL for anonymous player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}, "404": {"description": "- 51: Could not find entity\n- 300: Game not found\n"}}}}, "/terminals/gamecategories": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "deprecated": true, "summary": "Gets list of game categories", "description": "Gets list of all available game categories for a site", "parameters": [{"$ref": "#/parameters/includeGames"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/gameGroup"}], "responses": {"200": {"description": "List of game categories", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCategoryShortInfo"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/terminals/gamecategories/{gameCategoryId}/games": {"parameters": [{"$ref": "#/parameters/gameCategoryInPath"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}], "get": {"security": [{"apiKey": []}], "tags": ["Game"], "deprecated": true, "summary": "Gets list of games by game category public id", "description": "Method gets list of all avaliable game in a game category", "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 400: Pagination offset is not valid\n- 401: Pagination limit is not valid\n- 403: Key is not valid for sort by\n- 404: Sort order is not valid\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/terminals/players/external/page": {"post": {"tags": ["Authentication"], "security": [{"apiKey": []}], "summary": "Gets login, registration, player info or change password page", "description": "Gets login, registration, player info or change password page. Header should contain \"x-terminal-token\" with brandId.", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GetMerchantPageRequestInfo"}}], "responses": {"200": {"description": "Page url and extra info", "schema": {"$ref": "#/definitions/GetMerchantPageResponseInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n- 201: Password does not match\n"}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}, "500": {"description": "- 505: Merchant should have url and password in params\n- 505: Merchant internal error\n"}}}}, "/terminals/games/live/{provider}/live-info": {"get": {"security": [{"apiKey": []}], "tags": ["Lobby"], "deprecated": true, "summary": "Gets Live Casino game (table) info", "description": "Method returns list of live table games info", "parameters": [{"name": "provider", "in": "path", "type": "string", "description": "Live game provider", "required": true}, {"$ref": "#/parameters/tableId__in"}], "responses": {"200": {"description": "Live game info for tables", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameLive"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/terminals/live-manager/url": {"get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "deprecated": true, "summary": "Get live manager URL", "description": "Get live manager URL for a entity. Header should contain \"x-terminal-token\" with brandId", "responses": {"200": {"description": "live manager url", "schema": {"$ref": "#/definitions/GetLiveManagerUrl"}}, "400": {"description": "- 903: Dynamic domain is not defined\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/terminals/refresh-captcha/{username}": {"get": {"tags": ["Authentication"], "security": [{"apiKey": []}], "summary": "Refresh <PERSON>", "description": "Gets new captcha. Header should contain \"x-terminal-token\" with brandId", "parameters": [{"name": "username", "in": "path", "type": "string", "description": "User name", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON>", "schema": {"$ref": "#/definitions/Captcha"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 204: Access token error\n- 208: Terminal token error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK"}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}}, "definitions": {"Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 60}, "message": {"type": "string", "description": "error message", "example": "Entity already exists"}}}, "Captcha": {"type": "object", "properties": {"csrfToken": {"type": "string", "description": "csrf token", "example": "rVkdd8zBZxMo2Tno86ZCv/yV38hfTDI5+jigqVHG/lNXvYu+wmt7vPYjDSV7EJovg5JNMfEXLsb1Ls9GUqdxUzZVCpPZKjAup6S8NTT6rU1k83FUj0BQLefTyvkMdvVFCmCmfUstOfY6T/mGj6pYzl8ebd8GFI8IKIQbzgV2TDLWzWQgHXKSI55NI+5f8w=="}, "image": {"type": "string", "description": "Image (presented as base64)"}}}, "TerminalData": {"type": "object", "properties": {"id": {"type": "string", "description": "Terminal public id", "example": "vOq32eRb"}, "title": {"type": "string", "description": "Terminal title", "example": "Golendon Indie Club No003 Terminal 2018-04-21-No0010056"}, "status": {"type": "string", "description": "status of terminal (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "brandId": {"type": "string", "description": "Entity public id", "example": "gT78JJg9"}, "lobbyId": {"type": "string", "description": "Lobby public id", "example": "Qlw12kI8"}, "createdAt": {"type": "string", "description": "The time when a terminal is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time when a terminal is updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "player": {"type": "object", "description": "Information about active player if it exists"}}}, "TerminalCreateData": {"type": "object", "properties": {"title": {"type": "string", "description": "Terminal title", "example": "Golendon Indie Club No003 Terminal 2018-04-21-No0010056"}, "status": {"type": "string", "description": "status of terminal (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "lobbyId": {"type": "string", "description": "Lobby public id", "example": "Qlw12kI8"}}}, "LobbyData": {"type": "object", "properties": {"id": {"type": "string", "description": "Lobby public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Lobby title", "example": "Some lobby title"}, "description": {"type": "string", "description": "Lobby description", "example": "Some lobby description"}, "status": {"type": "string", "description": "status of lobby (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "info": {"type": "object", "description": "Any meta information of lobby", "properties": {"liveManagerUrl": {"type": "object", "properties": {"url": {"type": "string"}, "path": {"type": "string"}}}, "menuItems": {"type": "array", "items": {"$ref": "#/definitions/LobbyMenuItem"}}}, "example": {"thumb": "http://meme.info/nooooo.png"}}, "createdAt": {"type": "string", "description": "time when a Lobby was created (ISO 8601 timestamp)", "example": "2018-09-19T12:30:49.083Z"}, "updatedAt": {"type": "string", "description": "last time a Lobby was updated (ISO 8601 timestamp)", "example": "2018-09-19T12:33:06.909Z"}}}, "LoginPlayerInfo": {"type": "object", "required": ["code", "token"], "properties": {"code": {"type": "string", "description": "code of player", "example": "Pl0039SrvInd04VIP02"}, "token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "isPasswordTemp": {"type": "boolean", "description": "indicate whether password is temporary. If no could be omitted", "example": true}}}, "LoginPlayerDataInfo": {"type": "object", "required": ["code", "password"], "properties": {"code": {"type": "string", "description": "player's code", "example": "PL0034SrvCN02"}, "password": {"type": "string", "format": "password", "description": "password is longer than or equal 8 letters and contains at least one letter, one uppercase letter and one digit", "example": "19Letters&4Numbers&3Signs!"}, "csrfToken": {"type": "string", "description": "csrf token", "example": "rVkdd8zBZxMo2Tno86ZCv/yV38hfTDI5+jigqVHG/lNXvYu+wmt7vPYjDSV7EJovg5JNMfEXLsb1Ls9GUqdxUzZVCpPZKjAup6S8NTT6rU1k83FUj0BQLefTyvkMdvVFCmCmfUstOfY6T/mGj6pYzl8ebd8GFI8IKIQbzgV2TDLWzWQgHXKSI55NI+5f8w=="}, "captchaToken": {"type": "string", "description": "<PERSON><PERSON> must match the captcha", "example": "12345"}, "force": {"type": "boolean", "description": "<PERSON>p another active session", "example": true}}}, "LoginExternalPlayerInfo": {"type": "object", "required": ["code", "token"], "properties": {"code": {"type": "string", "description": "code of player", "example": "Pl0039SrvInd04VIP02"}, "token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "customerSessionId": {"type": "boolean", "description": "external customer sessionId from", "example": "IjoxLCJyGeS2VlkWQiVudyJ1cl0eU1c2VOjEsIm"}}}, "LoginExternalPlayerDataInfo": {"type": "object", "properties": {"ticket": {"type": "string", "description": "merchant’s ticket, used for authentication and getting session ID", "example": "mrch0fwd"}}}, "GetMerchantPageRequestInfo": {"type": "object", "required": ["pageType"], "properties": {"lobbyId": {"type": "string", "description": "player's lobby public id (optional)", "example": "0oKs76Bh"}, "pageType": {"type": "string", "description": "type of page to get - login, registration, player info or password change/recovery", "example": "registration", "enum": ["login", "registration", "playerinfo", "password"]}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "token": {"type": "string", "description": "player's token for playerinfo page (optional, used for player info only)", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}}}, "GetLiveManagerUrl": {"type": "object", "properties": {"url": {"type": "string", "description": "live-manager url", "example": "http://localhost:3010"}, "path": {"type": "string", "description": "namespace for live-manager", "example": "live-manager"}}}, "GetMerchantPageResponseInfo": {"type": "object", "properties": {"url": {"type": "string", "description": "player's lobby public id (optional)", "example": "0oKs76Bh"}, "size": {"type": "number", "description": "size of page, optional", "example": 1024}}}, "GameCategoryItem": {"type": "object", "required": ["type"], "properties": {"id": {"type": "string", "description": "Object publicId or code", "example": "sw_fufish"}, "type": {"type": "string", "description": "Type of object - game, provider, label, intersection", "example": "game"}, "items": {"type": "array", "description": "This attribute use with intersection type", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "W4RkGRen"}, "type": {"type": "string", "example": "label"}}}}}}, "GameCategoryShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "brandId": {"type": "string", "description": "Entity public id", "example": "gT78JJg9"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "type": {"type": "string", "description": "Type of game category, can be general or gamestore. In most of cases it's general", "example": "general"}, "ordering": {"type": "number", "description": "used to safe order of game categories", "example": 0}, "isEntityOwner": {"type": "boolean", "description": "Indicates is entity owner of category", "example": true}, "games": {"type": "object", "description": "Games inside category", "additionalProperties": {"$ref": "#/definitions/GamesTerminalInfo"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoriesTranslationItem": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "example": "title EU"}, "description": {"type": "string", "example": "description in EU"}, "icon": {"type": "string", "example": "icon in EU"}}}, "GamesTerminalInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "providerTitle": {"type": "string", "description": "provider title", "example": "test"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "features": {"$ref": "#/definitions/GameFeatures"}, "live": {"$ref": "#/definitions/GameLive"}}}, "LobbyMenuItem": {"type": "object", "properties": {"title": {"type": "string", "description": "Lobby menu item name for EN", "example": "Some category"}, "icon": {"type": "string", "description": "Lobby menu item icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, icon for all languages except EN", "additionalProperties": {"type": "object", "properties": {"title": {"type": "string", "example": "Some category"}, "icon": {"type": "string", "example": "text"}}}}, "games": {"type": "array", "description": "Games inside menu item", "items": {"$ref": "#/definitions/LobbyGameInfo"}}}}, "LobbyGameInfo": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "defaultInfo": {"type": "object", "properties": {"images": {"type": "object", "properties": {"poster": {"type": "string"}}}, "screenshots": {"type": "array", "items": {"type": "string"}}, "screenshots_hd": {"type": "array", "items": {"type": "string"}}}}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "features": {"type": "object", "properties": {"live": {"$ref": "#/definitions/Live"}}}}}, "GameInfo": {"type": "object", "required": ["code", "defaultInfo", "info"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "info": {"type": "object", "description": "game info by locale", "additionalProperties": {"$ref": "#/definitions/GameDescription"}}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "labels": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}, "providerCode": {"type": "string", "description": "Provider code", "example": "PR"}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "status": {"type": "string", "description": "suspended or normal (by default)", "example": "normal", "enum": ["normal", "suspended"]}, "settings": {"type": "object", "description": "game settings - any key-value pairs"}, "royalties": {"type": "number", "description": "Royalties for an entity game", "example": 0.15}, "releaseDate": {"type": "string", "description": "date when game was added into system", "example": "2018-08-22T12:28:51.382Z"}, "jackpots": {"$ref": "#/definitions/GameJackpots"}, "live": {"$ref": "#/definitions/GameLive"}}}, "GameFeatures": {"type": "object", "description": "<PERSON><PERSON> with features", "properties": {"isGRCGame": {"type": "boolean", "example": true}, "customNewFeature": {"type": "number", "example": 1}, "live": {"$ref": "#/definitions/Live"}, "jackpotTypes": {"type": "array", "items": {"type": "string"}}, "ignoreJackpotTypesValidation": {"type": "boolean", "description": "Enable or disabled jackpot types validation on entityGame level", "example": false}, "transferEnabled": {"type": "boolean", "example": true}, "isFreebetSupported": {"type": "boolean", "example": true}, "isBonusCoinsSupported": {"type": "boolean", "example": true}, "isMarketplaceSupported": {"type": "boolean", "example": true}, "supportsMarketingJP": {"type": "boolean", "example": true}, "supportsRtpConfigurator": {"type": "boolean", "example": true}, "isFunModeNotSupported": {"type": "boolean", "example": true}, "currenciesSupport": {"type": "array", "items": {"type": "string"}}, "rtp": {"type": "number", "example": 1}, "translations": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}}, "validateRequestsExtensionEnabled": {"type": "boolean", "description": "true if game is compatible with request validation extension", "example": true}}}, "LiveTable": {"type": "object", "required": ["tableId", "provider"], "properties": {"tableId": {"type": "string"}, "tableName": {"type": "string"}, "provider": {"type": "string"}, "providerSettings": {"type": "object"}}}, "Live": {"type": "object", "allOf": [{"$ref": "#/definitions/LiveTable"}], "properties": {"type": {"type": "string", "enum": ["baccarat", "roulette"], "description": "Type of live rush games", "example": "baccarat"}, "tables": {"type": "array", "description": "Live tables", "items": {"$ref": "#/definitions/LiveTable"}}}}, "GameDescription": {"type": "object", "description": "game info", "required": ["name", "description"], "properties": {"name": {"type": "string", "description": "game name", "example": "Slot Name"}, "description": {"type": "string", "description": "game description", "example": "Slot description"}}}, "LimitsByCurrencyCode": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Limits"}, "example": {"USD": {"maxTotalStake": 2000, "stakeAll": [1, 2, 3, 5], "stakeDef": 1, "stakeMax": 100, "stakeMin": 1, "winMax": 200}, "CNY": {"maxTotalStake": 3000, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 200, "stakeMin": 2, "winMax": 400}}}, "LabelInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "public id of label", "example": "7hJHT4RN"}, "title": {"type": "string", "description": "Label name", "example": "html5"}, "group": {"type": "string", "description": "Label group: platform, class or feature", "example": "platform"}}}, "ProviderLimitsByCurrencyCode": {"type": "object", "description": "Limits per currency", "additionalProperties": {"$ref": "#/definitions/LimitsObject"}}, "LimitsObject": {"type": "object", "description": "Limits"}, "Limits": {"type": "object", "required": ["maxTotalStake", "stakeAll", "stakeDef", "stakeMax", "stakeMin", "winMax"], "properties": {"maxTotalStake": {"type": "number", "description": "max total stake", "example": 1000}, "stakeAll": {"type": "array", "description": "all possible stake", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "stakeDef": {"type": "number", "description": "default stake", "example": 1}, "stakeMax": {"type": "number", "description": "max stake", "example": 5}, "stakeMin": {"type": "number", "description": "min stake", "example": 0.1}, "winMax": {"type": "number", "description": "max win", "example": 2000}}}, "GameJackpots": {"type": "object", "description": "jackports of game", "additionalProperties": {"type": "object", "description": "jackpot info for game", "properties": {"currency": {"type": "string", "example": "USD"}, "id": {"type": "string", "example": "sw-jpgame"}, "pools": {"type": "object", "additionalProperties": {"type": "object", "properties": {"amount": {"type": "number", "example": 120}}}}}}, "example": {"sw-jpgame": {"currency": "USD", "id": "sw-jpgame", "pools": {"pool0": {"amount": 122}}}}}, "GameLive": {"type": "object", "description": "live info for game", "properties": {"id": {"type": "string", "description": "table id", "example": "mock-0-1"}, "provider": {"type": "string", "description": "table provider", "example": "mock"}, "dealer": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON>"}, "picture": {"type": "string", "example": "http://picture.com/mock.jpeg"}}}, "status": {"type": "string", "description": "table status", "example": "online"}, "type": {"type": "number", "description": "table game type", "example": 0}}, "example": {"id": "mock-0-1", "provider": "mock", "dealer": {"name": "<PERSON><PERSON>", "picture": "http://picture.com/mock.jpeg"}, "status": "online", "type": 0}}, "PlayerGameURLInfo": {"type": "object", "required": ["url", "token"], "properties": {"url": {"type": "string", "description": "game URL for specific player", "example": "http://super_game.com/"}, "token": {"type": "string", "format": "byte", "description": "player token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}}, "parameters": {"currency": {"name": "currency", "in": "query", "description": "Return limits of game only for selected currency", "required": false, "type": "string"}, "includeGamesAmount": {"name": "includeGamesAmount", "in": "query", "description": "Include games count available for entity in game category", "required": false, "type": "boolean"}, "includeGames": {"name": "includeGames", "in": "query", "description": "Include games available for entity in game category", "required": false, "type": "boolean"}, "gameCategoryType": {"name": "type", "in": "query", "description": "Game category type, by default general is used", "required": false, "type": "string", "enum": ["general", "gamestore"]}, "terminalId": {"name": "terminalId", "in": "path", "description": "terminal's public id", "required": true, "type": "string"}, "lobbyId": {"name": "lobbyId", "in": "path", "description": "public id of lobby", "required": true, "type": "string"}, "image": {"name": "image", "in": "path", "description": "image name (icon, logo, loginBgImage, thumbBgImage)", "required": true, "type": "string"}, "gameCategoryInPath": {"name": "gameCategoryId", "in": "path", "description": "Game category public id", "required": true, "type": "string"}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "gameCategoryId": {"in": "query", "name": "gamecategoryId", "description": "Game category public id", "required": false, "type": "string"}, "gameCategoryId__in": {"in": "query", "name": "gamecategoryId__in", "description": "List of game category public ids separated by commas", "required": false, "type": "string"}, "onlyCategoryGames": {"in": "query", "name": "onlyCategoryGames", "description": "Return only games from any entity's category, true | false (default)", "required": false, "type": "string"}, "gameProviderId": {"in": "query", "name": "providerId", "description": "Game provider id", "required": false, "type": "string"}, "gameProviderCodeEquality": {"in": "query", "name": "providerCode", "description": "Game provider code equals to value", "required": false, "type": "string"}, "gameProviderCode__in": {"in": "query", "name": "providerCode__in", "description": "Game provider codes separated by commas", "required": false, "type": "string"}, "gameProviderTitleEquality": {"in": "query", "name": "providerTitle", "description": "Game provider title equals to value", "required": false, "type": "string"}, "gameProviderTitleContains": {"in": "query", "name": "providerTitle__contains", "description": "Game provider title contains string", "required": false, "type": "string"}, "gameProviderTitleNotContains": {"in": "query", "name": "providerTitle__contains!", "description": "Game provider title not contains string", "required": false, "type": "string"}, "codeStrictEquality": {"in": "query", "name": "code", "description": "code equals to value", "required": false, "type": "string"}, "titleStrictEquality": {"in": "query", "name": "title", "description": "title equals to value", "required": false, "type": "string"}, "titleContains": {"in": "query", "name": "title__contains", "description": "title contains string", "required": false, "type": "string"}, "titleNotContains": {"in": "query", "name": "title__contains!", "description": "title not contains string", "required": false, "type": "string"}, "jackpots": {"in": "query", "name": "jackpots", "description": "Append jackpots info to game info", "required": false, "type": "boolean"}, "includeLive": {"in": "query", "name": "includeLive", "description": "Append live info to game info", "required": false, "type": "boolean"}, "gameGroup": {"in": "query", "name": "gameGroup", "description": "Game group of player", "required": false, "type": "string"}, "isFreebetSupported": {"in": "query", "name": "isFreebetSupported", "description": "true if searched games should support freebets", "required": false, "type": "boolean"}, "isBonusCoinsSupported": {"in": "query", "name": "isBonusCoinsSupported", "description": "true if searched games should support bonus coins", "required": false, "type": "boolean"}, "transferEnabled": {"in": "query", "name": "transferEnabled", "description": "true if searched games should support transfer", "required": false, "type": "boolean"}, "isGRCGame": {"in": "query", "name": "isGRCGame", "description": "true if searched games should be GRC", "required": false, "type": "boolean"}, "jackpotTypes": {"in": "query", "name": "jackpotTypes", "description": "'true' or comma-separated list of jackpot types if searched games should have jackpots", "required": false, "type": "string"}, "live": {"in": "query", "name": "live", "description": "'true' or json string of live parameters if searched games should have live streaming", "required": false, "type": "string"}, "features": {"in": "query", "name": "features", "description": "json string of features object of searched games, see GameFeatures model for details", "required": false, "type": "string"}, "tableId__in": {"name": "tableId__in", "in": "query", "type": "string", "description": "table ids to get live info for separated with string. omit it to return all the tables", "required": false}, "limitsCurrency": {"name": "currency", "in": "query", "type": "string", "description": "[ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code for fetching only this currency limits", "required": false}, "gameCode": {"name": "gameCode", "in": "path", "description": "Game code", "required": true, "type": "string"}, "cashier": {"name": "cashier", "in": "query", "description": "cashier site", "required": false, "type": "string"}, "lobby": {"name": "lobby", "in": "query", "description": "lobby site", "required": false, "type": "string"}, "languageInQuery": {"name": "language", "in": "query", "description": "Language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "required": false, "type": "string", "pattern": "^[a-z]{2}(-[a-z]{2})?$"}}}