import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, StaticDomain, DomainStatus, StaticDomainType } from "../entities/domain";

export class DynamicDomainModel extends Model<
    InferAttributes<DynamicDomainModel>,
    InferCreationAttributes<DynamicDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: string | null;
    declare provider: string | null;
    declare status: DomainStatus;
    declare expiryDate: Date | null;
    declare environment: string | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            environment: this.environment,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: { field: "provider", type: DataTypes.STRING, allowNull: true },
        status: { field: "status", type: DataTypes.ENUM(...Object.values(DomainStatus)), allowNull: false },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "dynamic_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}

export class StaticDomainModel extends Model<
    InferAttributes<StaticDomainModel>,
    InferCreationAttributes<StaticDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: string | null;
    declare provider: string | null;
    declare status: DomainStatus;
    declare type: StaticDomainType;
    declare expiryDate: Date | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            type: this.type,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

StaticDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: { field: "provider", type: DataTypes.STRING, allowNull: true },
        status: { field: "status", type: DataTypes.ENUM(...Object.values(DomainStatus)), allowNull: false },
        type: { field: "type", type: DataTypes.ENUM(...Object.values(StaticDomainType)), allowNull: false },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "static_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getStaticDomainModel() {
    return StaticDomainModel;
}
