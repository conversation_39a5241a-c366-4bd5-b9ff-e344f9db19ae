import { DynamicDomain, StaticDomain } from "./domain";
import { FindOptions } from "sequelize";
import { LobbyDomainAttributes } from "./lobbyDomain";

export interface StaticDomainPoolItemAttributes {
    isActive?: boolean;
    staticDomainId?: number;
    staticDomainPoolId?: number;
}

export interface StaticDomainPoolLobbyItemAttributes {
    isActive?: boolean;
    lobbyDomainId?: number;
    staticDomainPoolId?: number;
}

export interface DynamicDomainPoolItemAttributes {
    isActive?: boolean;
    dynamicDomainId?: number;
    dynamicDomainPoolId?: number;
}

export interface DomainPoolService {
    create(data: StaticDomainPoolAttributes): Promise<StaticDomainPoolAttributes>;

    update(id: number, data: Partial<StaticDomainPoolAttributes>): Promise<StaticDomainPoolAttributes>;

    findById(id: number): Promise<StaticDomainPoolAttributes>;

    findAll(findOptions?: FindOptions<unknown>): Promise<StaticDomainPoolAttributes[]>;

    remove(id: number): Promise<void>;

    addDomain(poolId: number, domainId: number): Promise<StaticDomainPoolItemAttributes>;

    removeDomain(poolId: number, domainId: number): Promise<void>;

    enableDomain(poolId: number, domainId: number): Promise<void>;

    disableDomain(poolId: number, domainId: number): Promise<void>;
}

export interface DynamicDomainPoolService {
    create(data: DynamicDomainPoolAttributes): Promise<DynamicDomainPoolAttributes>;

    update(id: number, data: Partial<DynamicDomainPoolAttributes>): Promise<DynamicDomainPoolAttributes>;

    findById(id: number): Promise<DynamicDomainPoolAttributes>;

    findAll(findOptions?: FindOptions<unknown>): Promise<DynamicDomainPoolAttributes[]>;

    remove(id: number): Promise<void>;

    addDomain(poolId: number, domainId: number): Promise<DynamicDomainPoolItemAttributes>;

    removeDomain(poolId: number, domainId: number): Promise<void>;

    enableDomain(poolId: number, domainId: number): Promise<void>;

    disableDomain(poolId: number, domainId: number): Promise<void>;
}

export interface LobbyDomainPoolService {
    addLobbyDomain(poolId: number, domainId: number): Promise<StaticDomainPoolLobbyItemAttributes>;

    removeLobbyDomain(poolId: number, domainId: number): Promise<void>;

    enableLobbyDomain(poolId: number, domainId: number): Promise<void>;

    disableLobbyDomain(poolId: number, domainId: number): Promise<void>;
}

export interface ExtendedStaticDomain extends StaticDomain {
    isActive?: boolean;
    StaticDomainPoolItem?: { // connection table reference
        isActive?: boolean;
    };
}

export interface ExtendedDynamicDomain extends DynamicDomain {
    isActive?: boolean;
    DynamicDomainPoolItem?: { // connection table reference
        isActive?: boolean;
    };
}

export interface ExtendedLobbyDomain extends LobbyDomainAttributes {
    isActive?: boolean;
    StaticDomainPoolLobbyItem?: { // connection table reference
        isActive?: boolean;
    };
}

export interface StaticDomainPoolAttributes {
    id?: number;
    name: string;
    inherited?: boolean;
    domains?: ExtendedStaticDomain[];
    lobbyDomains?: ExtendedLobbyDomain[];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DynamicDomainPoolAttributes {
    id?: number;
    name: string;
    domains?: ExtendedDynamicDomain[];
    createdAt?: Date;
    updatedAt?: Date;
}
