export enum DomainStatus {
    ACTIVE = "Active",
    SUSPENDED = "Suspended"
}

export enum StaticDomainType {
    STATIC = "Static",
    LOBBY = "Lobby",
    LIVE_STREAMING = "Live Streaming",
    EHUB = "Ehub"
}

export interface Domain {
    id?: number;
    domain: string;
    description?: string;
    provider?: string;
    status: DomainStatus;
    expiryDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DynamicDomain extends Domain {
    environment: string;
}

export interface StaticDomain extends Domain {
    type: StaticDomainType;
}
