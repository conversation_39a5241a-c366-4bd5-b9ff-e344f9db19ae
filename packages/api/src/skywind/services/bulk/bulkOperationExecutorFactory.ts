import { Merchant } from "../../entities/merchant";
import * as Errors from "../../errors";
import { ProxyService } from "../proxy";
import {
    ENTITY_BULK_OPERATION_TYPE,
    BulkOperation,
    EntityExecutorData, EntityBulkOperation, EntityBulkOperationResult
} from "../../entities/bulk";
import { getDomainService } from "../domain";
import { MerchantImpl } from "../merchant";
import { BaseEntity } from "../../entities/entity";
import { FindOptions, Op, Transaction } from "sequelize";
import { ValidationError } from "../../errors";
import {
    BulkOperationExecutor,
    DynamicDomainOperationExecutor,
    ProxyOperationExecutor,
    StaticDomainOperationExecutor
} from "./executors";
import { isSetOperation } from "./utils";
import { Models } from "../../models/models";
import { DynamicDomain, StaticDomain } from "../../entities/domain";
import { Proxy } from "../../entities/proxy";

const model = Models.MerchantModel;

export interface BulkOperationExecutorFactory<Operation extends BulkOperation, Result> {
    isValidOperationType?(type: string): boolean;

    createExecutors(entity: BaseEntity,
                    operations: Operation[],
                    transaction: Transaction): Promise<BulkOperationExecutor<Operation, Result>[]>;
}

export class EntityExecutorFactory
    implements BulkOperationExecutorFactory<EntityBulkOperation, EntityBulkOperationResult> {

    protected proxyService = new ProxyService();
    private typeNames: string[] = Object.values(ENTITY_BULK_OPERATION_TYPE);

    public isValidOperationType(type: string): boolean {
        return this.typeNames.includes(type);
    }

    public async createExecutors(
        entity: BaseEntity,
        operations: EntityBulkOperation[],
        transaction: Transaction): Promise<BulkOperationExecutor<EntityBulkOperation, EntityBulkOperationResult>[]> {

        const data = await this.loadExecutorData(entity, operations, transaction);

        const initExecutors = (op: EntityBulkOperation) => {
            switch (op.item.type) {
                case ENTITY_BULK_OPERATION_TYPE.DYNAMIC:
                    return new DynamicDomainOperationExecutor(op, data);
                case ENTITY_BULK_OPERATION_TYPE.STATIC:
                    return new StaticDomainOperationExecutor(op, data);
                case ENTITY_BULK_OPERATION_TYPE.PROXY:
                    return new ProxyOperationExecutor(op, data);
                default:
                    throw new ValidationError(`Invalid operation item type - ${op.item.type}`);
            }
        };

        return operations.map(initExecutors);
    }

    private async loadExecutorData(
        entity: BaseEntity,
        operations: EntityBulkOperation[],
        transaction: Transaction): Promise<EntityExecutorData> {

        const staticDomains = await this.getDomains(ENTITY_BULK_OPERATION_TYPE.STATIC, operations) as StaticDomain[];
        const merchants = await this.getMerchants(entity, operations);
        const proxies = await this.getProxies(operations);
        const dynamicDomains = await this.getDomains(
            ENTITY_BULK_OPERATION_TYPE.DYNAMIC, operations, transaction) as DynamicDomain[];

        return {
            staticDomains,
            merchants,
            proxies,
            dynamicDomains
        };
    }

    private async getMerchants(entity: BaseEntity, operations: BulkOperation[]): Promise<Merchant[]> {
        const ids = operations.reduce((acc: number[], op: BulkOperation): number[] => {
            if (op.item.type === ENTITY_BULK_OPERATION_TYPE.PROXY) {
                acc.push(entity.find({ key: op.entityKey }).id);
            }
            return acc;
        }, []);

        if (!ids.length) {
            return [];
        }

        const items = await model.findAll({
            where: {
                brandId: {
                    [Op.in]: ids
                }
            }
        });

        if (items.length !== new Set(ids).size) {
            return Promise.reject(new ValidationError("Merchants not found"));
        }

        return items.map(c => new MerchantImpl(c));
    }

    private async getProxies(operations: BulkOperation[]): Promise<Proxy[]> {
        const ids = this.getIdsByType(operations, ENTITY_BULK_OPERATION_TYPE.PROXY);

        if (!ids.length) {
            return [];
        }

        const proxies = await this.proxyService.list({
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });

        if (proxies.length !== ids.length) {
            return Promise.reject(new ValidationError("Proxies not found"));
        }

        return proxies.map(proxy => proxy.toInfo());
    }

    private async getDomains(type: ENTITY_BULK_OPERATION_TYPE.STATIC, operations: BulkOperation[], transaction?: Transaction): Promise<StaticDomain[]>;
    private async getDomains(type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC, operations: BulkOperation[], transaction?: Transaction): Promise<DynamicDomain[]>;
    private async getDomains(type: ENTITY_BULK_OPERATION_TYPE.STATIC | ENTITY_BULK_OPERATION_TYPE.DYNAMIC,
                             operations: BulkOperation[],
                             transaction?: Transaction): Promise<StaticDomain[] | DynamicDomain[]> {

        const domainIds: number[] = this.getIdsByType(operations, type);

        if (!domainIds.length) {
            return [];
        }

        const findOptions: FindOptions<any> = { where: { id: { [Op.in]: domainIds }}};
        if (transaction) {
            findOptions.transaction = transaction;
            findOptions.lock = transaction.LOCK.SHARE;
        }

        const domains = await getDomainService(type as any).findAll(findOptions);

        if (domainIds.length !== domains.length) {
            return Promise.reject(new Errors.ValidationError("Domains not found"));
        }

        return domains as StaticDomain[] | DynamicDomain[];
    }

    private getIdsByType(operations: BulkOperation[],
                         type: ENTITY_BULK_OPERATION_TYPE): number[] {

        const ids = operations.reduce((acc: number[], op: BulkOperation): number[] => {
            if (op.item.type === type && isSetOperation(op)) {
                acc.push(op.item.id);
            }
            return acc;
        }, []);

        return [...new Set(ids)];
    }
}
