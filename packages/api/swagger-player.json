{"swagger": "2.0", "info": {"description": "Skywind - API for Player", "version": "5.55", "title": "Skywind - API for Player"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-player-token", "in": "header"}}, "paths": {"/logout": {"post": {"tags": ["Registration and Authentication"], "security": [{"apiKey": []}], "summary": "Log player out", "description": "Logs player out under the key entity", "responses": {"204": {"description": "Logout OK"}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/refresh": {"post": {"tags": ["Registration and Authentication"], "security": [{"apiKey": []}], "summary": "Refreshes access player token before expiration", "description": "Refreshes access player token before expiration. Header should contain \"x-player-token\".", "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginPlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/info": {"get": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "summary": "Get player info", "description": "Gets general information of brand or external player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}, "patch": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "summary": "Update player info", "description": "Update general player's information", "parameters": [{"$ref": "#/parameters/UpdatePlayer"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 113: Player personal information cannot be saved\n- 712: Player is suspended\n- 850: Nickname is already used\n- 851: Nickname must be different from your login name\n- 852: Nickname must contain only English letters, numbers and keyboard special symbols (_!#$%&*, etc.)\n- 853: Nickname must contain a minimum of [length] symbols\n- 854: Nickname cannot exceed [length] symbols\n- 855: Nickname contains a word or phrase that is not allowed\n- 862: Number of attempts to change nickname exceeded\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/suspended": {"put": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "summary": "Suspend Player", "description": "Player suspends himself and then have no possibility to unsuspend. Only operator can change status of player after this operation.", "responses": {"204": {"description": "Player suspended"}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n- 225: Player info has not changed\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/password": {"post": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "summary": "Set new password", "description": "Sets new player password for logged in players", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["password", "newPassword"], "properties": {"password": {"type": "string", "description": "player's current (old) password", "example": "All1Cats2Are*Beautiful!ACAB"}, "newPassword": {"type": "string", "description": "player's new password", "example": "All%Coders$Are1Beautiful!ACAB"}}}}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "- 10: Access token is missing\n- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n- 228: New password should be different from a previous one\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 201: Password does not match\n- 204: Access token error\n- 205: Access Token is expired\n- 223: Player created without password\n- 224: Password has not changed\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/promo": {"get": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "parameters": [{"name": "type", "in": "query", "description": "type of promotions to fetch. leave blank for all types", "required": false, "type": "string", "enum": ["freebet", "virtual_money", "bonus_coin"]}], "summary": "Gets list of player promotions", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/history/game": {"get": {"tags": ["Game History"], "security": [{"apiKey": []}], "summary": "Get Game History for Player", "description": "The method returns game history items for the Player. Default output is limited to 20 entries per page. Max output is 100 entries per page. This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/roundsForLiveGames"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeContains"}, {"$ref": "#/parameters/gameCodeNotContains"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: <PERSON><PERSON><PERSON>,\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/history/unfinished/game": {"get": {"tags": ["Game History"], "security": [{"apiKey": []}], "summary": "Gets unfinished game rounds for Player", "description": "The method returns unfinished game rounds for Player.", "parameters": [{"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/unfinishedRoundStatus"}, {"$ref": "#/parameters/includeBrokenSpin"}, {"$ref": "#/parameters/gameContextId"}], "responses": {"200": {"description": "Return unfinished game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/UnfinishedGameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/history/game/{roundId}": {"get": {"tags": ["Game History"], "security": [{"apiKey": []}], "summary": "Get Round information for a Player", "description": "The method enables the player to get the game round information (such as bet and win amount, currency, was the round finished or no, etc.)", "parameters": [{"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"name": "roundId", "in": "path", "description": "Round ID", "required": true, "type": "string"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/history/game/{roundId}/events/{eventId}": {"get": {"tags": ["Game History"], "security": [{"apiKey": []}], "summary": "Get Round spin details information for a Player", "description": "The method enables the player to get the game round spin details information (such as bet and win amount, currency, was the round finished or no, etc.)", "parameters": [{"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"name": "roundId", "in": "path", "description": "Round ID", "required": true, "type": "string"}, {"name": "eventId", "in": "path", "description": "Event ID", "required": true, "type": "string"}], "responses": {"200": {"description": "Event details", "schema": {"$ref": "#/definitions/EventDetails"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/payments/methods": {"get": {"tags": ["Payments"], "security": [{"apiKey": []}], "summary": "Gets list of payment methods", "description": "Get all available payment methods for player's brand", "parameters": [{"name": "type", "in": "query", "description": "payment type", "required": true, "type": "string"}], "responses": {"200": {"description": "List of payment methods", "schema": {"type": "array", "items": {"$ref": "#/definitions/PaymentInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n"}}}}, "/payments": {"get": {"tags": ["Payments"], "security": [{"apiKey": []}], "summary": "Get list of payment order details for player", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'startDate' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/paymentMethodCodeStrictEquality"}, {"$ref": "#/parameters/paymentMethodCodeIn"}, {"$ref": "#/parameters/orderTypeIn"}, {"$ref": "#/parameters/startDate"}, {"$ref": "#/parameters/startDate__gt"}, {"$ref": "#/parameters/startDate__lt"}, {"$ref": "#/parameters/endDate"}, {"$ref": "#/parameters/endDate__gt"}, {"$ref": "#/parameters/endDate__lt"}, {"$ref": "#/parameters/orderStatus"}, {"$ref": "#/parameters/trxId"}, {"$ref": "#/parameters/extTrxId"}], "responses": {"200": {"description": "PaymentOrder info", "schema": {"type": "array", "items": {"$ref": "#/definitions/PaymentOrderInfo"}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n"}}}}, "/payments/deposits": {"post": {"tags": ["Payments"], "security": [{"apiKey": []}], "summary": "Initialize deposit payment method details", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PaymentMethodDetails"}}], "responses": {"200": {"description": "Payment method entity", "schema": {"$ref": "#/definitions/PaymentInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 712: Player is suspended\n- 736: Entity is under maintenance, but maintenance url is not defined\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n"}}}}, "/payments/withdrawals": {"post": {"tags": ["Payments"], "security": [{"apiKey": []}], "summary": "Initialize withdrawal payment method details", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PaymentMethodDetails"}}], "responses": {"200": {"description": "Payment method entity", "schema": {"$ref": "#/definitions/PaymentInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 712: Player is suspended\n- 736: Entity is under maintenance, but maintenance url is not defined\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n"}, "500": {"description": "- 301: Payment API transient error\n"}}}}, "/games/{gameCode}": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets player game URL", "description": "The method enables the player to play a particular game in real mode", "parameters": [{"$ref": "#/parameters/ipHeader"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/playMode"}, {"$ref": "#/parameters/playmode"}, {"$ref": "#/parameters/cashier"}, {"$ref": "#/parameters/lobby"}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 306: Game is suspended\n- 690: Bonus coins not available\n- 703: IP address cannot be resolved\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 736: Entity is under maintenance, but maintenance url is not defined\n- 751: Referrer is missing\n- 902: Static domain is not defined\n- 903: Dynamic domain is not defined\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 701: Country of IP is restricted\n- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n- 502: Merchant not found\n"}}}}, "/gamecategories": {"get": {"tags": ["Game Categories"], "security": [{"apiKey": []}], "summary": "Gets list of game categories", "description": "Gets list of all available game categories for a site", "parameters": [{"$ref": "#/parameters/includeGames"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/gameGroup"}], "responses": {"200": {"description": "List of game categories", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCategoryShortInfo"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/lobbies/{lobbyId}": {"get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "summary": "Gets lobby by public id", "description": "Gets lobby extended info by public id.", "parameters": [{"$ref": "#/parameters/lobbyId"}, {"$ref": "#/parameters/includeGamesLimits"}, {"$ref": "#/parameters/SocketVersion"}, {"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "Lobbies extended info", "schema": {"$ref": "#/definitions/LobbyData"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/lobbies/{lobbyId}/menu-items": {"get": {"tags": ["Lobby"], "security": [{"apiKey": []}], "summary": "Gets lobby by public id", "description": "Gets lobby extended info by public id.", "parameters": [{"$ref": "#/parameters/lobbyId"}, {"$ref": "#/parameters/includeGamesLimits"}, {"$ref": "#/parameters/SocketVersion"}, {"name": "fields", "in": "query", "description": "A comma-separated list of LobbyData property paths you want returned. Path can be in dot notation, like \"info.liveManagerUrl\". Omit it to return all the properties", "type": "string"}], "responses": {"200": {"description": "Lobbies extended info", "schema": {"$ref": "#/definitions/LobbyData"}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/games": {"get": {"tags": ["Game"], "security": [{"apiKey": []}], "summary": "Gets list of games", "description": "Gets games for entity, filtered by lobby's public id", "parameters": [{"name": "lobbyId", "in": "query", "description": "lobby's public id", "required": true, "type": "string"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/status__in"}, {"name": "fields", "in": "query", "description": "A comma-separated list of GameTerminalInfo fields you want returned", "type": "string"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameTerminalInfo"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 11: Terminal Access Token is missing\n- 208: Terminal token error\n"}}}}, "/favoritegames": {"get": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Gets list of favorite games", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/gameCodeOnly"}], "responses": {"200": {"description": "List of favorite games", "schema": {"type": "array", "items": {"$ref": "#/definitions/FavoriteGameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/favoritegames/{gameCode}": {"post": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Add favorite game ", "parameters": [{"$ref": "#/parameters/gameCode"}], "responses": {"201": {"description": "<PERSON> favorite game"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Remove favorite game ", "parameters": [{"$ref": "#/parameters/gameCode"}], "responses": {"200": {"description": "Unmark favorite game"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/recentlygames": {"get": {"security": [{"apiKey": []}], "tags": ["Game"], "summary": "Get list of recently played games", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortOrder"}], "responses": {"200": {"description": "List of recently played games", "schema": {"type": "array", "items": {"$ref": "#/definitions/RecentlyGameInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/external/info": {"get": {"tags": ["Player External (Deprecated)"], "security": [{"apiKey": []}], "summary": "Get external player info", "description": "Method gets external player's information with balances. This method is out-of-date use just /info instead of /external/info", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n- 502: Merchant not found\n"}}}, "put": {"tags": ["Player External (Deprecated)"], "security": [{"apiKey": []}], "parameters": [{"$ref": "#/parameters/UpdateExternalPlayer"}], "summary": "Update external player info", "description": "Method allow update external player's information. This method is out-of-date use just /info instead of /external/info", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n- 300: Game not found\n- 502: Merchant not found\n"}}}}, "/players/validate-nickname/{playerNickname}": {"parameters": [{"name": "playerNickname", "in": "path", "type": "string", "description": "player nickname", "required": true}], "get": {"tags": ["Get and Edit Data"], "security": [{"apiKey": []}], "summary": "check valid player nickname", "responses": {"204": {"description": "is valid player nickname"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/responsiblegaming": {"get": {"tags": ["Responsible Gaming"], "security": [{"apiKey": []}], "summary": "Get player responsible gaming settings", "description": "Get player responsible gaming settings", "responses": {"200": {"description": "Responsible gaming settings", "schema": {"$ref": "#/definitions/ResponsibleGamingPlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 746: Can't execute operation. Responsible gaming is not enabled.\n- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}, "patch": {"tags": ["Responsible Gaming"], "security": [{"apiKey": []}], "summary": "Updates player responsible gaming settings", "description": "Updates player responsible gaming settings", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/UpdateResponsibleGamingPlayerSettings"}}], "responses": {"200": {"description": "Responsible gaming settings", "schema": {"$ref": "#/definitions/ResponsibleGamingPlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 746: Can't execute operation. Responsible gaming is not enabled.\n- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}, "delete": {"tags": ["Responsible Gaming"], "security": [{"apiKey": []}], "summary": "Delete player responsible gaming pending setting", "description": "Delete player responsible gaming pending setting before it become active", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DeletePendingResponsibleGamingPlayerSetting"}}], "responses": {"200": {"description": "Responsible gaming settings", "schema": {"$ref": "#/definitions/ResponsibleGamingPlayerInfo"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 712: Player is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access token is missing\n- 204: Access token error\n- 205: Access Token is expired\n"}, "403": {"description": "- 206: Forbidden\n- 746: Can't execute operation. Responsible gaming is not enabled.\n- 1501: Can't execute operation. Player is self-excluded.\n- 1502: Can't execute operation. Player has reached his deposit limit.\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 105: Session not found\n- 106: Session already finished\n"}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Checks server health", "responses": {"200": {"description": "Health check OK", "schema": {"type": "object", "properties": {"serverName": {"type": "string", "description": "Name of current server", "example": "Player"}}}}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}}, "definitions": {"EventDetails": {"type": "object", "required": ["roundId", "spinNumber", "gameId", "gameVersion"], "properties": {"roundId": {"type": "integer", "description": "Round identifier", "example": 200000134}, "spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "gameId": {"type": "string", "description": "Game module id", "example": "sw_gol"}, "gameVersion": {"type": "string", "description": "Game module version", "example": "0.1.1"}, "details": {"type": "string", "description": "Game event details", "example": "{data: \"Some spin data\"}"}, "initSettings": {"type": "string", "description": "Game init settings", "example": "{data: \"Some game init information\"}"}, "credit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 10}, "debit": {"type": "number", "description": "change of balance not connected with game's win/bet", "example": 5}}}, "ProviderLimitsByCurrencyCode": {"type": "object", "description": "Limits per currency", "additionalProperties": {"$ref": "#/definitions/LimitsObject"}}, "LimitsObject": {"type": "object", "description": "Limits"}, "Limits": {"type": "object", "required": ["maxTotalStake", "stakeAll", "stakeDef", "stakeMax", "stakeMin", "winMax"], "properties": {"maxTotalStake": {"type": "number", "description": "max total stake", "example": 1000}, "stakeAll": {"type": "array", "description": "all possible stake", "items": {"type": "number"}, "example": [0.1, 0.5, 1, 2, 3, 5]}, "stakeDef": {"type": "number", "description": "default stake", "example": 1}, "stakeMax": {"type": "number", "description": "max stake", "example": 5}, "stakeMin": {"type": "number", "description": "min stake", "example": 0.1}, "winMax": {"type": "number", "description": "max win", "example": 2000}}}, "GameDescription": {"type": "object", "description": "game info", "required": ["name", "description"], "properties": {"name": {"type": "string", "description": "game name", "example": "Slot Name"}, "description": {"type": "string", "description": "game description", "example": "Slot description"}}}, "LimitsByCurrencyCode": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Limits"}, "example": {"USD": {"maxTotalStake": 2000, "stakeAll": [1, 2, 3, 5], "stakeDef": 1, "stakeMax": 100, "stakeMin": 1, "winMax": 200}, "CNY": {"maxTotalStake": 3000, "stakeAll": [2, 3, 5, 10], "stakeDef": 2, "stakeMax": 200, "stakeMin": 2, "winMax": 400}}}, "GameFeatures": {"type": "object", "description": "<PERSON><PERSON> with features", "properties": {"isGRCGame": {"type": "boolean", "example": true}, "customNewFeature": {"type": "number", "example": 1}, "live": {"$ref": "#/definitions/Live"}, "jackpotTypes": {"type": "array", "items": {"type": "string"}}, "ignoreJackpotTypesValidation": {"type": "boolean", "description": "Enable or disabled jackpot types validation on entityGame level", "example": false}, "transferEnabled": {"type": "boolean", "example": true}, "isFreebetSupported": {"type": "boolean", "example": true}, "isBonusCoinsSupported": {"type": "boolean", "example": true}, "isMarketplaceSupported": {"type": "boolean", "example": true}, "supportsMarketingJP": {"type": "boolean", "example": true}, "supportsRtpConfigurator": {"type": "boolean", "example": true}, "isFunModeNotSupported": {"type": "boolean", "example": true}, "currenciesSupport": {"type": "array", "items": {"type": "string"}}, "rtp": {"type": "number", "example": 1}, "translations": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}}, "validateRequestsExtensionEnabled": {"type": "boolean", "description": "true if game is compatible with request validation extension", "example": true}}}, "LiveTable": {"type": "object", "required": ["tableId", "provider"], "properties": {"tableId": {"type": "string"}, "tableName": {"type": "string"}, "provider": {"type": "string"}, "providerSettings": {"type": "object"}}}, "Live": {"type": "object", "allOf": [{"$ref": "#/definitions/LiveTable"}], "properties": {"type": {"type": "string", "enum": ["baccarat", "roulette"], "description": "Type of live rush games", "example": "baccarat"}, "tables": {"type": "array", "description": "Live tables", "items": {"$ref": "#/definitions/LiveTable"}}}}, "GameCategoryShortInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "Game category public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Game category name for EN", "example": "Some category"}, "description": {"type": "string", "description": "Game category description for EN", "example": "Some category description"}, "brandId": {"type": "string", "description": "Entity public id", "example": "gT78JJg9"}, "status": {"type": "string", "description": "Game category status. normal | suspended", "example": "normal"}, "type": {"type": "string", "description": "Type of game category, can be general or gamestore. In most of cases it's general", "example": "general"}, "ordering": {"type": "number", "description": "used to safe order of game categories", "example": 0}, "isEntityOwner": {"type": "boolean", "description": "Indicates is entity owner of category", "example": true}, "games": {"type": "object", "description": "Games inside category", "additionalProperties": {"$ref": "#/definitions/GameTerminalInfo"}}, "icon": {"type": "string", "description": "Game category icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, description and icon for all languages except for EN", "properties": {"eu": {"$ref": "#/definitions/GameCategoriesTranslationItem"}}}}}, "GameCategoryItem": {"type": "object", "required": ["type"], "properties": {"id": {"type": "string", "description": "Object publicId or code", "example": "sw_fufish"}, "type": {"type": "string", "description": "Type of object - game, provider, label, intersection", "example": "game"}, "items": {"type": "array", "description": "This attribute use with intersection type", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "W4RkGRen"}, "type": {"type": "string", "example": "label"}}}}}}, "GameTerminalInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "providerTitle": {"type": "string", "description": "provider title", "example": "test"}, "defaultInfo": {"$ref": "#/definitions/GameDescription"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}, "features": {"$ref": "#/definitions/GameFeatures"}, "live": {"$ref": "#/definitions/GameLive"}}}, "GamesLimits": {"type": "object", "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "limits": {"$ref": "#/definitions/LimitsByCurrencyCode"}}}, "GameLive": {"type": "object", "description": "live info for game", "properties": {"id": {"type": "string", "description": "table id", "example": "mock-0-1"}, "provider": {"type": "string", "description": "table provider", "example": "mock"}, "dealer": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON><PERSON>"}, "picture": {"type": "string", "example": "http://picture.com/mock.jpeg"}}}, "status": {"type": "string", "description": "table status", "example": "online"}, "type": {"type": "number", "description": "table game type", "example": 0}}, "example": {"id": "mock-0-1", "provider": "mock", "dealer": {"name": "<PERSON><PERSON>", "picture": "http://picture.com/mock.jpeg"}, "status": "online", "type": 0}}, "LoginPlayerInfo": {"type": "object", "required": ["code", "token"], "properties": {"code": {"type": "string", "description": "code of player", "example": "Pl0039SrvInd04VIP02"}, "token": {"type": "string", "format": "byte", "description": "access token", "example": "eyJ1c2VySWQiOjEsImVudGl0eUlkIjoxLCJ1c2V"}, "isPasswordTemp": {"type": "boolean", "description": "indicate whether password is temporary. If no could be omitted", "example": true}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 719}, "message": {"type": "string", "description": "error message", "example": "Player can not play against himself"}}}, "PlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "id": {"type": "string", "description": "public id of player", "example": "481eAS0d"}, "status": {"type": "string", "description": "player status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "isTest": {"type": "boolean", "description": "is player created only for testing", "example": false}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "brandTitle": {"type": "string", "description": "title of brand", "example": "<PERSON><PERSON> Lett Win"}, "agentId": {"type": "string", "description": "agent's public id", "example": "jdG8Sem9"}, "agentTitle": {"type": "string", "description": "agent's title", "example": "brother<PERSON>ite"}, "agentDomain": {"type": "string", "description": "agent's domain", "example": "example.com"}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}}}, "PlayerInfoWithBalances": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "id": {"type": "string", "description": "public id of player", "example": "481eAS0d"}, "status": {"type": "string", "description": "player status (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)", "example": "US"}, "currency": {"type": "string", "description": "currency code [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217)", "example": "USD"}, "language": {"type": "string", "description": "language code [ISO 639-1](https://en.wikipedia.org/wiki/ISO_639-1)", "example": "en"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "league", "keyName": "faction", "value": "Noasauridae"}]}, "isTest": {"type": "boolean", "description": "is player created only for testing", "example": false}, "lastLogin": {"type": "string", "description": "The last time a user logged on (ISO 8601 timestamp)", "example": "2016-12-10T16:47:38.887Z"}, "createdAt": {"type": "string", "description": "The time when a user is created (ISO 8601 timestamp)", "example": "2016-12-10T12:45:32.324Z"}, "updatedAt": {"type": "string", "description": "The last time a user updated (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "brandTitle": {"type": "string", "description": "title of brand", "example": "<PERSON><PERSON> Lett Win"}, "agentId": {"type": "string", "description": "agent's public id", "example": "jdG8Sem9"}, "agentTitle": {"type": "string", "description": "agent's title", "example": "brother<PERSON>ite"}, "agentDomain": {"type": "string", "description": "agent's domain", "example": "example.com"}, "balances": {"$ref": "#/definitions/Balances"}, "isOnline": {"type": "boolean", "description": "Online / Offline player status", "example": false}, "isVip": {"type": "boolean", "description": "VIP player or not", "example": false}, "historyUrl": {"type": "string", "description": "Game history url", "example": "url"}, "currencyFormatConfig": {"$ref": "#/definitions/CurrencyFormatConfig"}}}, "Balance": {"type": "object", "required": ["main"], "properties": {"main": {"type": "number", "description": "balance", "example": 1020.36}}}, "Balances": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Balance"}, "example": [{"USD": 1020.36}, {"CNY": 3600.23}]}, "GameHistory": {"type": "object", "properties": {"roundId": {"type": "string", "description": "Round public id", "example": 100000063}, "brandId": {"type": "string", "description": "Brand public id", "example": "feE3Sb39"}, "playerCode": {"type": "string", "description": "Player code", "example": "PLAYER1"}, "gameCode": {"type": "string", "description": "Game code", "example": "sw_mrmnky"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 2}, "revenue": {"type": "number", "description": "Revenue", "example": -1.9}, "firstTs": {"type": "string", "description": "time of first action in round", "example": "2017-07-14T07:07:01.080Z"}, "ts": {"type": "string", "description": "time of last action in round", "example": "2017-07-14T07:07:11.930Z"}, "finished": {"type": "boolean", "description": "Whether the round has ended", "example": true}, "isTest": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "balanceBefore": {"type": "number", "description": "Player's balance before round", "example": 25000}, "balanceAfter": {"type": "number", "description": "Player's balance after round", "example": 24950}, "device": {"type": "string", "description": "Player's device", "example": "web"}, "insertedAt": {"type": "string", "example": "2018-07-04T11:14:03.275Z"}, "totalEvents": {"type": "number", "description": "Count of events", "example": 1}, "extraData": {"type": "object", "properties": {"extRoundId": {"type": "string", "description": "PT round id", "example": "extRound1"}}}}}, "GameHistorySpin": {"type": "object", "required": ["balanceAfter", "balanceBefore", "spinNumber", "type", "currency", "bet", "win", "endOfRound", "isPayment"], "properties": {"spinNumber": {"type": "integer", "description": "Spin number", "example": 200000134}, "type": {"type": "string", "description": "History item type", "example": "slot"}, "currency": {"type": "string", "description": "Currency code", "example": "USD"}, "bet": {"type": "number", "description": "Total bet", "example": 0.1}, "win": {"type": "number", "description": "Total winning", "example": 1.5}, "endOfRound": {"type": "boolean", "description": "Whether this spin was at the end of the round", "example": true}, "ts": {"type": "string", "description": "Time of spin (ISO 8601 timestamp)", "example": "2017-02-16T16:37:13.613Z"}, "test": {"type": "boolean", "description": "Whether the round has been created only for testing", "example": false}, "isPayment": {"type": "boolean", "description": "If it's true, spin has transaction"}, "balanceBefore": {"type": "number", "description": "Player balance before spin", "example": 1000}, "balanceAfter": {"type": "number", "description": "Player balance after spin", "example": 998.8}}}, "PlayerGameURLInfo": {"type": "object", "required": ["url", "token"], "properties": {"url": {"type": "string", "description": "game URL for specific player", "example": "http://super_game.com/"}, "token": {"type": "string", "format": "byte", "description": "start game token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}, "newPlayerToken": {"type": "string", "format": "byte", "description": "player token to access game", "example": "oJqXX2tkAADKn3MpcM9kVbVk53neuIYI62dEkYdYubl+9lyXRECjQww3VsmEPfMoUkO6uqB56WDPhPGdS3aGnQ"}}}, "UpdatePlayerData": {"type": "object", "properties": {"firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "<PERSON>"}, "nickname": {"type": "string", "description": "player nickname", "example": "<PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "customData": {"type": "object", "description": "custom player's data", "example": [{"key": "alias", "keyName": "pen name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}}, "UpdateExternalPlayerData": {"type": "object", "properties": {"nickname": {"type": "string", "description": "player nickname", "example": "Nickname"}}}, "PaymentOrderInfo": {"type": "object", "properties": {"trxId": {"type": "string"}, "brandId": {"type": "string"}, "brandTitle": {"type": "string"}, "playerCode": {"type": "string"}, "orderId": {"type": "string"}, "orderType": {"type": "string"}, "orderDate": {"type": "string"}, "orderInfo": {"type": "string"}, "orderStatus": {"type": "string"}, "currencyCode": {"type": "string"}, "amount": {"type": "number"}, "playerBalanceAfter": {"type": "number"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "paymentMethodCode": {"type": "string"}, "isTest": {"type": "boolean"}, "extTrxId": {"type": "string"}}}, "PaymentInfo": {"type": "object", "properties": {"type": {"type": "string", "description": "type of payment method", "example": "deposit"}, "code": {"type": "string", "description": "payment method code", "example": "paypal"}, "name": {"type": "string", "description": "payment method name", "example": "PayPal"}, "description": {"type": "string", "description": "description of payment method", "example": "the best payment method in the world"}, "status": {"type": "string", "description": "payment method status", "example": "suspended"}}}, "PaymentMethodDetails": {"type": "object", "required": ["paymentMethodCode", "currency"], "properties": {"paymentMethodCode": {"type": "string", "description": "Payment Method Code", "example": "PayPal"}, "amount": {"type": "number", "description": "Payment amount", "example": 10000}, "currency": {"type": "string", "description": "currency name", "example": "USD"}, "isTest": {"type": "boolean", "description": "Is test data", "example": false}}}, "ResponsibleGamingPlayerInfo": {"type": "object", "properties": {"playerCode": {"type": "string", "description": "player code", "example": "PL0001"}, "brandId": {"type": "string", "description": "public id of brand", "example": "Hh89Kw3E"}, "jurisdiction": {"type": "string", "description": "jurisdiction this settings are applicable to", "example": "UK"}, "settings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettings"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettings"}}}}}, "ResponsibleGamingPlayerSettings": {"allOf": [{"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, {"type": "object", "properties": {"lossLimitPending": {"type": "number", "description": "Loss limit pending value", "example": 900}, "lossLimitPendingTimeframe": {"type": "string", "description": "Loss limit pending value timeframe", "enum": ["daily", "weekly", "monthly"], "example": "weekly"}, "lossLimitPendingDate": {"type": "string", "description": "Date in ISO8601 when loss limit pending value will become active", "example": "2018-06-27T12:59:46.694Z"}, "depositLimitPending": {"type": "number", "description": "Deposit limit pending value", "example": 900}, "depositLimitPendingTimeframe": {"type": "string", "description": "Deposit limit pending value timeframe", "enum": ["daily", "weekly", "monthly"], "example": "weekly"}, "depositLimitPendingDate": {"type": "string", "description": "Date in ISO8601 when loss deposit pending value will become active", "example": "2018-06-27T12:59:46.694Z"}, "createdAt": {"type": "string", "description": "time when a record was created (ISO 8601 timestamp)", "example": "2016-02-23T12:45:42.324Z"}, "updatedAt": {"type": "string", "description": "last time a record was updated (ISO 8601 timestamp)", "example": "2017-03-08T05:15:54.213Z"}}}]}, "UpdateResponsibleGamingPlayerSettings": {"type": "object", "properties": {"casino": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}, "sport_bet": {"$ref": "#/definitions/ResponsibleGamingPlayerSettingsUpdateData"}}}, "ResponsibleGamingPlayerSettingsUpdateData": {"type": "object", "properties": {"realityCheck": {"type": "number", "description": "Reality check value. 20/40/60/120/180 mins", "example": 40}, "lossLimit": {"type": "number", "description": "Loss limit value", "example": 100}, "lossLimitTimeframe": {"type": "string", "description": "Loss limit timeframe. daily, weekly, or monthly", "enum": ["daily", "weekly", "monthly"], "example": "daily"}, "depositLimit": {"type": "number", "description": "Deposit limit value", "example": 100}, "depositLimitTimeframe": {"type": "string", "description": "Deposit limit timeframe. daily, weekly, or monthly", "enum": ["daily", "weekly", "monthly"], "example": "daily"}, "casinoTimeoutTillDate": {"type": "string", "description": "Date in ISO8601 till which player has forbidden himself to play", "example": "2018-06-27T12:59:46.694Z"}, "selfExclusionTillDate": {"type": "string", "description": "Date in ISO8601 till which player has forbidden himself to get into system", "example": "2018-06-27T12:59:46.694Z"}}}, "DeletePendingResponsibleGamingPlayerSetting": {"type": "object", "properties": {"casino": {"type": "object", "description": "Pending entry to delete", "example": {"depositLimit": "anyValue"}}}}, "PlayerFreebetPromotion": {"type": "object", "properties": {"promoId": {"type": "string", "description": "Promotion id", "example": "W4RkGRen"}, "freebets": {"type": "array", "items": {"type": "object", "properties": {"currency": {"type": "string", "description": "Currency code", "example": "USD"}, "amount": {"type": "number", "description": "Left count of free bets", "example": 5}, "games": {"type": "array", "description": "List of game codes where free bets can be played", "items": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Code of game"}, "coins": {"type": "array", "items": {"type": "object"}}}}, "example": {"gameCode": "sw_fr", "coins": [{"USD": {"coin": 0.01}}, {"CNY": {"coin": 0.1}}]}}, "startDate": {"type": "string", "description": "Timestamp when player can start using free bets", "example": "2019-12-23T15:00:00.000Z"}, "endDate": {"type": "string", "description": "Timestamp when free bets will expire", "example": "2019-12-24T16:00:00.000Z"}}}}}}, "UnfinishedGameHistory": {"allOf": [{"$ref": "#/definitions/GameHistory"}, {"type": "object", "required": ["gameContextId"], "properties": {"gameContextId": {"type": "string", "description": "Id of a game context of unfinished game", "example": "games:context:Ki6hY78r:PL0001:sw_al:web"}, "status": {"type": "string", "enum": ["broken", "unfinished"], "description": "Status of round", "example": "broken"}}}]}, "FavoriteGameInfo": {"type": "object", "description": "Favorite games", "additionalProperties": {"$ref": "#/definitions/GameTerminalInfo"}}, "RecentlyGameInfo": {"type": "object", "properties": {"gameCode": {"type": "string", "description": "Game code", "example": "sw_fufish"}, "updatedAt": {"type": "string", "description": "The last time a player played (ISO 8601 timestamp)", "example": "2016-12-10T16:15:54.213Z"}}}, "GameCategoriesTranslationItem": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "example": "title EU"}, "description": {"type": "string", "example": "description in EU"}, "icon": {"type": "string", "example": "icon in EU"}}}, "LobbyData": {"type": "object", "properties": {"id": {"type": "string", "description": "Lobby public id", "example": "pQ3513OE"}, "title": {"type": "string", "description": "Lobby title", "example": "Some lobby title"}, "description": {"type": "string", "description": "Lobby description", "example": "Some lobby description"}, "status": {"type": "string", "description": "status of lobby (normal/suspended)", "enum": ["normal", "suspended"], "example": "normal"}, "info": {"type": "object", "description": "Any meta information of lobby", "properties": {"liveManagerUrl": {"type": "object", "properties": {"url": {"type": "string"}, "path": {"type": "string"}}}, "menuItems": {"type": "array", "items": {"$ref": "#/definitions/LobbyMenuItem"}}}, "example": {"thumb": "http://meme.info/nooooo.png"}}, "createdAt": {"type": "string", "description": "time when a Lobby was created (ISO 8601 timestamp)", "example": "2018-09-19T12:30:49.083Z"}, "updatedAt": {"type": "string", "description": "last time a Lobby was updated (ISO 8601 timestamp)", "example": "2018-09-19T12:33:06.909Z"}}}, "LobbyMenuItem": {"type": "object", "properties": {"title": {"type": "string", "description": "Lobby menu item name for EN", "example": "Some category"}, "icon": {"type": "string", "description": "Lobby menu item icon for EN", "example": "text"}, "translations": {"type": "object", "description": "translations for title, icon for all languages except EN", "additionalProperties": {"type": "object", "properties": {"title": {"type": "string", "example": "Some category"}, "icon": {"type": "string", "example": "text"}}}}, "games": {"type": "array", "description": "Games inside menu item", "items": {"$ref": "#/definitions/LobbyGameInfo"}}}}, "LobbyGameInfo": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "game code", "example": "SX567"}, "type": {"type": "string", "description": "game type", "example": "slot"}, "title": {"type": "string", "description": "game title", "example": "Mr <PERSON>"}, "defaultInfo": {"type": "object", "properties": {"images": {"type": "object", "properties": {"poster": {"type": "string"}}}, "screenshots": {"type": "array", "items": {"type": "string"}}, "screenshots_hd": {"type": "array", "items": {"type": "string"}}}}, "providerTitle": {"type": "string", "description": "Provider title", "example": "Provider 1"}, "features": {"type": "object", "properties": {"live": {"$ref": "#/definitions/Live"}}}}}, "CurrencyFormatConfig": {"type": "object", "properties": {"showCurrency": {"type": "boolean", "description": "Whether it's needed or not to show currency sign"}, "decimalPartSeparatorCharacter": {"type": "string", "description": "Decimal part separator character (example: 1.00)"}, "decimalPartDigitsCount": {"type": "integer", "description": "Decimal part default digits count (example: 10.00)"}, "realPartSeparatorCharacter": {"type": "string", "description": "Real part separator character (example: 100,000.00)"}, "realPartSeparationDigitsCount": {"type": "integer", "description": "Split number real part with comma by defined digits count, processed from right to left (example: 1,000,000)"}, "appendCurrencyToLeft": {"type": "boolean", "description": "Append currency symbol to left or right"}, "shortMode": {"type": "boolean", "description": "Enables short mode postfixes K:10^3, M:10^6, B:10^9 (example $1.45K or $9.99M)"}, "tickupDecimalsAsLineBet": {"type": "boolean", "description": "Use decimals type for tickups the same as we use for the current line bet value"}, "decimalPartAppendType": {"type": "string", "description": "Sets displaying type of decimal part"}}}}, "parameters": {"lobbyId": {"name": "lobbyId", "in": "path", "description": "public id of lobby", "required": true, "type": "string"}, "status": {"name": "status", "in": "query", "description": "Entity game status", "required": false, "type": "string", "enum": ["normal", "test"]}, "status__in": {"in": "query", "name": "status__in", "description": "List of entity game statuses. Can be normal, test.", "required": false, "type": "string"}, "currency": {"name": "currency", "in": "query", "description": "Return limits of game only for selected currency", "required": false, "type": "string"}, "roundsForLiveGames": {"name": "roundsForLiveGames", "in": "query", "description": "Return rounds for live games and add game name to response", "required": false, "type": "boolean"}, "gameGroup": {"in": "query", "name": "gameGroup", "description": "Game group of player", "required": false, "type": "string"}, "includeGamesLimits": {"name": "includeGamesLimits", "in": "query", "description": "Include games limits", "required": false, "type": "boolean"}, "includeGamesAmount": {"name": "includeGamesAmount", "in": "query", "description": "Include games count available for entity in game category", "required": false, "type": "boolean"}, "includeGames": {"name": "includeGames", "in": "query", "description": "Include games available for entity in game category", "required": false, "type": "boolean"}, "gameCategoryType": {"name": "type", "in": "query", "description": "Game category type, by default general is used", "required": false, "type": "string", "enum": ["general", "gamestore"]}, "UpdatePlayer": {"name": "new values", "in": "body", "description": "Data for update player", "required": true, "schema": {"$ref": "#/definitions/UpdatePlayerData"}}, "offset": {"name": "offset", "in": "query", "description": "Result list offset", "required": false, "type": "integer", "default": 0}, "limit": {"name": "limit", "in": "query", "description": "Result list limit", "required": false, "type": "integer", "default": 20}, "sortBy": {"name": "sortBy", "in": "query", "description": "Sorting key", "required": false, "type": "string"}, "sortOrder": {"name": "sortOrder", "in": "query", "description": "Sorting order", "required": false, "type": "string", "enum": ["ASC", "DESC"]}, "gameCodeStrictEquality": {"in": "query", "name": "gameCode", "description": "game code equal to value", "required": false, "type": "string"}, "gameCodeContains": {"in": "query", "name": "gameCode__contains", "description": "game code contains string", "required": false, "type": "string"}, "gameCodeNotContains": {"in": "query", "name": "gameCode__contains!", "description": "game code doesn't contain string", "required": false, "type": "string"}, "gameCodeIn": {"in": "query", "name": "gameCode__in", "description": "list of game codes separated by commas", "required": false, "type": "string"}, "roundIdStrictEquality": {"in": "query", "name": "roundId", "description": "roundId equal to value", "required": false, "type": "string"}, "roundIdIn": {"in": "query", "name": "roundId__in", "description": "list of roundIds separated by commas", "required": false, "type": "string"}, "currencyStrictEquality": {"in": "query", "name": "currency", "description": "currency equal to value", "required": false, "type": "string"}, "currencyIn": {"in": "query", "name": "currency__in", "description": "currencies separated by commas", "required": false, "type": "string"}, "ts": {"in": "query", "name": "ts", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__gt": {"in": "query", "name": "ts__gt", "description": "time of activity is greater-than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lt": {"in": "query", "name": "ts__lt", "description": "time of activity is less-than in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "queryFinished": {"name": "finished", "in": "query", "description": "Get finished/unfinished rounds. true - finished, false - unfinished, undefined - all", "required": false, "type": "boolean"}, "bet": {"in": "query", "name": "bet", "description": "sharp value for bet", "required": false, "type": "number"}, "bet__gt": {"in": "query", "name": "bet__gt", "description": "bet is greater-than", "required": false, "type": "number"}, "bet__lt": {"in": "query", "name": "bet__lt", "description": "bet is less-than", "required": false, "type": "number"}, "bet__gte": {"in": "query", "name": "bet__gte", "description": "bet is greater-than-or-equal", "required": false, "type": "number"}, "bet__lte": {"in": "query", "name": "bet__lte", "description": "bet is less-than-or-equal", "required": false, "type": "number"}, "win": {"in": "query", "name": "win", "description": "sharp value for win", "required": false, "type": "number"}, "win__gt": {"in": "query", "name": "win__gt", "description": "win is greater-than", "required": false, "type": "number"}, "win__lt": {"in": "query", "name": "win__lt", "description": "win is less-than", "required": false, "type": "number"}, "win__gte": {"in": "query", "name": "win__gte", "description": "win is greater-than-or-equal", "required": false, "type": "number"}, "win__lte": {"in": "query", "name": "win__lte", "description": "win is less-than-or-equal", "required": false, "type": "number"}, "revenue": {"in": "query", "name": "revenue", "description": "sharp value for revenue", "required": false, "type": "number"}, "revenue__gt": {"in": "query", "name": "revenue__gt", "description": "revenue is greater-than", "required": false, "type": "number"}, "revenue__lt": {"in": "query", "name": "revenue__lt", "description": "revenue is less-than", "required": false, "type": "number"}, "revenue__gte": {"in": "query", "name": "revenue__gte", "description": "revenue is greater-than-or-equal", "required": false, "type": "number"}, "revenue__lte": {"in": "query", "name": "revenue__lte", "description": "revenue is less-than-or-equal", "required": false, "type": "number"}, "orderStatus": {"in": "query", "name": "orderStatus", "type": "string", "required": false, "description": "declined or approved (by default)", "enum": ["approved", "declined", "init", "blocked"]}, "paymentMethodCodeStrictEquality": {"in": "query", "name": "paymentMethodCode", "description": "payment method code equal to value", "required": false, "type": "string"}, "trxId": {"in": "query", "name": "trxId", "description": "transaction id", "required": false, "type": "string"}, "extTrxId": {"in": "query", "name": "extTrxId", "description": "transaction id", "required": false, "type": "string"}, "paymentMethodCodeIn": {"in": "query", "name": "paymentMethodCode__in", "description": "payment method codes separated by commas", "required": false, "type": "string"}, "orderTypeIn": {"in": "query", "name": "orderType__in", "description": "comma separated order types", "required": false, "type": "string"}, "startDate": {"in": "query", "name": "startDate", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startDate__gt": {"in": "query", "name": "startDate__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "startDate__lt": {"in": "query", "name": "startDate__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate": {"in": "query", "name": "endDate", "description": "sharp time of activity in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__gt": {"in": "query", "name": "endDate__gt", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "endDate__lt": {"in": "query", "name": "endDate__lt", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "amount_strict": {"in": "query", "name": "amount", "description": "sharp value for amount", "required": false, "type": "number"}, "amount__gt": {"in": "query", "name": "amount__gt", "description": "amount is greater-than", "required": false, "type": "number"}, "amount__lt": {"in": "query", "name": "amount__lt", "description": "amount is less-than", "required": false, "type": "number"}, "amount__gte": {"in": "query", "name": "amount__gte", "description": "amount is greater-than-or-equal", "required": false, "type": "number"}, "amount__lte": {"in": "query", "name": "amount__lte", "description": "amount is less-than-or-equal", "required": false, "type": "number"}, "gameCode": {"name": "gameCode", "in": "path", "description": "Game code", "required": true, "type": "string"}, "playmode": {"name": "playmode", "in": "query", "description": "playmode name \"fun\" | \"bns\" | \"real\" (be default) alias for playMode", "required": false, "type": "string"}, "cashier": {"name": "cashier", "in": "query", "description": "cashier site", "required": false, "type": "string"}, "lobby": {"name": "lobby", "in": "query", "description": "lobby site", "required": false, "type": "string"}, "ts__gte": {"in": "query", "name": "ts__gte", "description": "start date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "ts__lte": {"in": "query", "name": "ts__lte", "description": "end date in ISO 8601 timestamp (e.g. 2016-12-10T16:47:38.887Z)", "required": false, "type": "string"}, "playMode": {"name": "playMode", "in": "query", "description": "playmode name \"fun\" | \"bns\" | \"real\" (be default)", "required": false, "type": "string"}, "ipHeader": {"name": "X-Forwarded-For", "description": "Client IP address", "type": "string", "in": "header"}, "unfinishedRoundStatus": {"in": "query", "name": "status", "description": "status of unfinished round", "type": "string", "required": false, "enum": ["broken", "unfinished", "requireLogout"]}, "includeBrokenSpin": {"in": "query", "name": "includeBrokenSpin", "description": "true to include broken spin data as well", "type": "boolean", "required": false}, "gameContextId": {"in": "query", "name": "gameContextId", "description": "gameContextId", "type": "string", "required": false}, "gameCodeOnly": {"in": "query", "name": "gameCodeOnly", "description": "Return only game codes", "required": false, "type": "boolean"}, "UpdateExternalPlayer": {"name": "info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateExternalPlayerData"}}, "SocketVersion": {"name": "socketVersion", "in": "query", "description": "This parameter is used to load the required version of the socket.io library. By default, version 2 of socket.io is used.", "required": false, "type": "string", "enum": ["v2", "v4"]}}}