{"swagger": "2.0", "info": {"description": "Skywind - API for Ban Words", "version": "5.55", "title": "Skywind - API for Ban Words"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Tokens contains ban words information", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-ban-words-token", "in": "header"}}, "paths": {"/players/validate-nickname/{playerNickname}": {"get": {"tags": ["Players"], "security": [{"apiKey": []}], "summary": "Check player nickname", "parameters": [{"name": "playerNickname", "in": "path", "description": "Player nickname", "required": true, "type": "string"}], "responses": {"204": {"description": "Player nickname status", "schema": {}}, "400": {"description": "- 40: Validation error"}}}}, "/token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Generates token", "responses": {"200": {"description": "Ban words token", "schema": {"$ref": "#/definitions/BanWordsTokenInfo"}}}}}}, "definitions": {"Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 677}, "message": {"type": "string", "description": "error message", "example": "Negative transaction operation value"}}}, "BanWordsTokenInfo": {"type": "object", "properties": {"banWordsToken": {"type": "string", "example": "token"}}}}}