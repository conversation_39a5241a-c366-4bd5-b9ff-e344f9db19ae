{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - API", "version": "5.55", "title": "Skywind - Galaxy Pro - API"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"id:encode": "Encode id", "id:decode": "Decode id", "live-chat": "Ability to have live chat in BO", "entity": "Entity management", "entity:edit": "Update entity", "entity:create": "Create entity", "entity:change-state": "Change status of entity", "entity:change-state-test": "Change 'test' status of entity", "entity:view": "View entity", "entity:delete": "Delete entity", "entity:balance": "View balances", "entity:info": "Display entity info", "entity:move": "Move entity to another parent", "entity:gameclose:forcefinish": "Allows to force finish round", "entity:gameclose:revert": "Allows to revert round", "entity:gameclose:retry": "Allows to retry pending operation for round", "entity:gameclose:transfer-out": "Allows to transfer-out for round", "entity:gameclose:finalize": "Allows to finalize round", "country": "Country management", "country:add": "Add country to entity", "country:remove": "Remove country to entity", "currency": "Currency management", "currency:add": "Add currency to entity", "currency:remove": "Remove currency to entity", "domain": "Domain management", "domain:static": "Manage static domain", "domain:static:view": "View static domains", "domain:static:create": "Add static domains", "domain:static:remove": "Remove static domains", "domain:static:edit": "Edit static domains", "domain:dynamic": "Manage dynamic domain", "domain:dynamic:view": "View dynamic domains", "domain:dynamic:create": "Add dynamic domains", "domain:dynamic:remove": "Remove dynamic domains", "domain:dynamic:edit": "Edit dynamic domains", "domain-pool": "Domain pool management", "domain-pool:static": "Static domain pool management", "domain-pool:static:view": "View static domain pools", "domain-pool:static:create": "Create static domain pools", "domain-pool:static:edit": "Edit static domain pools", "domain-pool:static:remove": "Remove static domain pools", "domain-pool:dynamic": "Static domain pool management", "domain-pool:dynamic:view": "View static domain pools", "domain-pool:dynamic:create": "Create static domain pools", "domain-pool:dynamic:edit": "Edit static domain pools", "domain-pool:dynamic:remove": "Remove static domain pools", "language": "Language management", "language:add": "Add language to entity", "language:remove": "Remove language to entity", "user": "User management", "user:edit": "Edit user details", "user:view": "View entity's users", "user:create": "Add user to entity", "user:change-state": "Change status of user", "user:change-type": "Change type of user", "user-extra:login-unlock": "Unlock user login", "player-extra:login-unlock": "Unlock player login", "user-extra:change-password-unlock": "Unlock user changing password", "user:change-password": "Change user password", "user-extra:delete": "Delete user by username", "user-extra:force-reset-password": "Reset user password without old", "user-extra:email:force-set": "Change user email", "permissions": "Manage user permissions", "permissions:view": "View user's permissions", "finance": "Finance management", "finance:debit": "Make debit", "finance:credit": "Make credit", "finance:view": "View debit/credit history", "bi:reports:view": "View available Business Intelligence reports", "bi:report:player": "View players Business Intelligence reports", "bi:report:game": "View games Business Intelligence reports", "bi:report:currency": "View currencies Business Intelligence reports", "bi:report:top-games": "View top games Business Intelligence reports", "bi:report:ggr": "View ggr Business Intelligence reports", "bi:report:grc": "View grc Business Intelligence reports", "bi:report:jackpot-brief": "View brief jackpots Business Intelligence reports", "bi:report:jackpots": "View jackpots Business Intelligence reports", "bi:report:finance": "View deposits/withdrawals Business Intelligence reports", "bi:report:promo": "View promo Business Intelligence reports", "bi:report:transactions": "View game transactions summary Business Intelligence reports", "bi:report:tournaments": "View game tournaments summary Business Intelligence reports (Phantom)", "bi:report:bets-distribution": "View bets-distribution Business Intelligence reports", "bi:report:live-stream-provider-report": "Live Stream Provider Report", "bi:report:summary-real-time-advanced": "View Summary Real Time Advanced reports", "bi:report:jackpot-split-prize": "Jackpot Split Prize", "bi:report:player-leaving": "Leaving players report", "bi:report:jackpot:mustwin": "Must win jackpot report", "bi:report:gvc-daily": "GVC daily report", "bi:report:special:esl:campaign": "BO report for special ESL campaign", "bi:report:freebet-game": "Freebets Games report", "bi:report:golden-matrix-by-country": "Golden Matrix Report", "bi:report:player-show-hide-column:debits-credits": "Show/hide Debits and Credits columns in Player report", "bi:report:prize-drops": "Prize Drops BI report", "bi:report:unfinished-rounds": "Unfinished rounds report", "bi:report:billing": "Billing BI report", "bi:report:analytics": "Analytics BI report", "bi:report:freebets": "Freebets BI report", "gamecategory:view": "View game categories", "gamecategory": "Manage game categories", "gamecategory:create": "Create game categories", "gamecategory:edit": "Update game categories", "gamecategory:delete": "Delete game categories", "gamecategory:change-ordering": "Change order of game category", "player": "Player management", "player:create": "Create player", "player:view": "View player", "player:edit": "Update player", "player:change-state": "Change status of player", "player:login": "Login player", "player:promotion": "Manage player promotions", "player:bonus": "Manage player bonuses", "player:bonus:view": "View player bonuses", "player:bonus:create": "Create player bonuses", "player:bonus:delete": "Delete player bonuses", "player:deposit": "Deposit to player", "player:withdrawal": "<PERSON><PERSON>wal player", "responsiblegaming:player": "Manage player's responsible gaming settings", "responsiblegaming:player:view": "View player's responsible gaming settings", "responsiblegaming:player:edit": "Update player's responsible gaming settings", "responsiblegaming:player:delete": "Delete player's responsible gaming pending settings changes", "entity:game": "Manage game", "entity:game:view": "View game", "entity:game:change-state": "Enable disable game for entity", "entity:game:change-state:disabled": "Disable game for entity", "entity:game:change-state:enabled": "Enable game for entity", "entity:game:limits": "Apply game limit filters for entity and child entities", "entity:game:url": "Get game URL for player", "entity:game:aams-code": "Display AAMS code", "entity:game:add-game-cascade": "Add game cascade", "entity:game:delete-cascade": "Remove game from entity with child entity games", "disable:entity:game-history:balances": "Hide balance before balance after in game history", "entitydomain": "Manage entitydomains", "entitydomain:static": "Manage static entitydomains", "entitydomain:static:view": "View static entitydomains", "entitydomain:static:remove": "Remove static entitydomains", "entitydomain:static:create": "Create static entitydomains", "entitydomain:static:edit": "Edit static entitydomains", "entitydomain:static:tags": "Manage static domain tags", "entitydomain:static:tags:set": "Set static domain tags", "entitydomain:static:tags:reset": "Reset static domain tags", "entitydomain:dynamic": "Manage dynamic entitydomains", "entitydomain:dynamic:view": "View dynamic entitydomains", "entitydomain:dynamic:remove": "Remove dynamic entitydomains", "entitydomain:dynamic:create": "Create dynamic entitydomains", "entitydomain:dynamic:edit": "Edit dynamic entitydomains", "entity:external-game-provider:history": "Get external (game provider) history report", "entity:external-game-provider:availability": "Get external game providers that are currently in use by entity", "role": "Manage roles", "role:create": "Create role", "role:edit": "Edit role details", "role:delete": "Delete role", "role:view": "View role", "role:move": "Relocate role", "lobby:downloadable": "Manage downloadable lobby", "agent": "Manage agents", "agent:view": "View agents", "site": "Manage site access", "entity:game:history": "Get game history report", "entity:game:unfinished": "Get unfinished game rounds", "audit": "View audit log", "payment": "Payment methods management", "payment:view": "View payment methods of entity", "payment:edit": "Edit payment method of entity", "payment:create": "Add payment methods of entity", "payment:execute": "Start/init payment on entity", "payment:add-key": "Add public key to gateway", "payment:get-key": "Get public key from gateway", "payment:transfer-in": "Transfering money to player", "payment:transfer-out": "Transfering money from player", "report": "Get Entity brand report", "report:currency": "Get Entity brand currency report based on game rounds", "report:wallet-currency": "Get Entity brand currency report based on wallet data", "report:players": "Get Entity players report", "report:games": "Get Entity games daily report", "report:ggr": "Get Entity ggr total report", "report-without-limit": "Get reports without any limits (like Time limits)", "report:promo": "Get Entity brand promotion report", "report:jackpot": "Get Entity brand jackpot report", "report:jackpot:contributions": "Get Entity contributions to jackpots", "report:jackpot:contributions:players": "Get Entity player's contribution to jackpots", "report:jackpot:contributions:logs": "Get entity's jackpot contribution logs", "report:jackpot:contributions:wins": "Get entity's jackpot win logs", "jackpot": "Manage jackpot types and instances", "jackpot:type": "Manage jackpot types", "jackpot:type:view": "View jackpot types", "jackpot:type:edit": "Edit jackpot types", "jackpot:type:create": "Create jackpot types", "jackpot:type:delete": "Delete jackpot types", "jackpot:instance": "Manage jackpot instances", "jackpot:instance:view": "View jackpot instances", "jackpot:instance:edit": "Edit jackpot instances", "jackpot:instance:create": "Create jackpot instances", "jackpot:instance:delete": "Delete jackpot instances", "keyentity:jackpot": "Manage keyentity jackpot types and instances", "keyentity:jackpot:instance": "Manage keyentity jackpot instances", "keyentity:jackpot:instance:view": "View keyentity jackpot instances", "keyentity:jackpot:instance:edit": "Edit keyentity jackpot instances", "keyentity:jackpot:instance:create": "Create keyentity jackpot instances", "keyentity:jackpot:instance:delete": "Delete keyentity jackpot instances", "jurisdiction": "Manage jurisdictions", "jurisdiction:view": "View jurisdictions", "jurisdiction:create": "Create jurisdictions", "jurisdiction:delete": "Delete jurisdictions", "playersession:find": "Find player session", "playersession:kill": "Kill player session", "gamertp": "Manage Game RTP", "gamertp:view": "Game RTP view", "integrationtests": "Manage integration tests", "integrationtests:view": "View reports on the results of completed integration tests", "integrationtests:run": "The ability to run integration tests", "keyentity:gamertp": "Manage Game RTP for key entity", "keyentity:gamertp:view": "Game RTP view for key entity", "keyentity:user": "User management of key entity", "keyentity:user:edit": "Edit user details of key entity", "keyentity:user:view": "View users of key entity", "keyentity:user:create": "Add user to key entity", "keyentity:user:change-state": "Change status of key entity's user", "keyentity:user:change-type": "Change user type for keyentity", "keyentity:user-extra:login-unlock": "Unlock key entity's user login", "keyentity:player-extra:login-unlock": "Unlock key entity's player login", "keyentity:user-extra:change-password-unlock": "Unlock key entity's user changing password", "keyentity:user:change-password": "Change key entity's user password", "keyentity:user-extra:delete": "Delete user by username", "keyentity:user-extra:force-reset-password": "Reset password without old password", "keyentity:user-extra:email:force-set": "Change key entity's user email", "keyentity:permissions": "Manage key entity's user permissions", "keyentity:permissions:view": "View key entity's user permissions", "keyentity:bi:reports": "Manage Business Intelligence reports", "keyentity:bi:reports:view": "View available Business Intelligence reports", "keyentity:bi:report:player": "View players Business Intelligence reports", "keyentity:bi:report:game": "View games Business Intelligence reports", "keyentity:bi:report:currency": "View currencies Business Intelligence reports", "keyentity:bi:report:top-games": "View top games Business Intelligence reports", "keyentity:bi:report:ggr": "View ggr Business Intelligence reports", "keyentity:bi:report:grc": "View grc Business Intelligence reports", "keyentity:bi:report:jackpot-brief": "View brief jackpots Business Intelligence reports", "keyentity:bi:report:jackpots": "View jackpots Business Intelligence reports", "keyentity:bi:report:finance": "View deposits/withdrawals Business Intelligence reports", "keyentity:bi:report:promo": "View promo Business Intelligence reports", "keyentity:bi:report:transactions": "View game transactions summary Business Intelligence reports", "keyentity:bi:report:tournaments": "View game tournaments summary Business Intelligence reports (Phantom)", "keyentity:bi:report:bets-distribution": "View bets-distribution Business Intelligence reports", "keyentity:bi:report:live-stream-provider-report": "Live Stream Provider Report", "keyentity:bi:report:summary-real-time-advanced": "View Summary Real Time Advanced reports", "keyentity:bi:report:jackpot-split-prize": "Jackpot Split Prize", "keyentity:bi:report:player-leaving": "Leaving players report", "keyentity:bi:report:golden-matrix-by-country": "Golden Matrix by country report ", "keyentity:bi:report:jackpot:mustwin": "Must win jackpot report", "keyentity:bi:report:gvc-daily": "GVC daily report", "keyentity:bi:report:special:esl:campaign": "BO report for special ESL campaign", "keyentity:bi:report:freebet-game": "Freebets Games report", "keyentity:bi:report:prize-drops": "Prize Drops BI report", "keyentity:bi:report:unfinished-rounds": "Key entity's unfinished rounds report", "keyentity:bi:report:player-show-hide-column:debits-credits": "Show/hide Debits and Credits columns in Player report", "keyentity:bi:report:billing": "Billing BI report", "keyentity:bi:report:analytics": "Analytics BI report", "keyentity:bi:report:freebets": "Freebets BI report", "keyentity:bi-reports-domains": "Manage BI report domains", "keyentity:bi-reports-domains:create": "Create BI report domains", "keyentity:bi-reports-domains:view": "View BI report domains", "keyentity:bi-reports-domains:edit": "Edit BI report domains", "keyentity:bi-reports-domains:select": "Select BI report domains", "keyentity:bi-reports-domains:delete": "Delete BI report domains", "keyentity:player": "Key entity's player management", "keyentity:player:create": "Create player under the key entity", "keyentity:player:view": "View player under the key entity", "keyentity:player:edit": "Update player under the key entity", "keyentity:player:deposit": "Deposit to key entity's player", "keyentity:player:withdrawal": "With<PERSON>wal key entity's player", "keyentity:player:change-state": "Change status of key entity's player", "keyentity:player:login": "Login player under the key entity", "keyentity:player:promotion": "Manage player promotions", "keyentity:player:bulk-operation": "Use player bulk operations", "keyentity:player:bonus": "Manage player bonuses", "keyentity:player:bonus:view": "View player bonuses", "keyentity:player:bonus:create": "Create player bonuses", "keyentity:player:bonus:delete": "Delete player bonuses", "keyentity:gamegroup": "Manage game groups", "keyentity:gamegroup:view": "View game groups", "keyentity:gamegroup:create": "Create gamegroups, manage limits", "keyentity:gamegroup:edit": "Update gamegroups, manage limits", "keyentity:gamegroup:delete": "Remove gamegroups, manage limits", "keyentity:gamegroup:rename": "Rename gamegroup", "keyentity:game": "Manage game", "keyentity:game:view": "View game", "keyentity:game:change-state": "Change status of game", "keyentity:game:change-state:disabled": "Change status of game to disabled", "keyentity:game:change-state:enabled": "Change status of game to enabled", "keyentity:game:limits": "Apply game limit filters", "keyentity:game:url": "Get game URL for player", "keyentity:game:history": "Get game history report", "keyentity:game:unfinished": "Get unfinished game rounds", "keyentity:game:aams-code": "Display AAMS code", "disable:keyentity:game-history:balances": "Hide balance before balance after in game history for keyentity", "keyentity:gameprovider": "Game provider management", "keyentity:gameprovider:view": "Get list of game providers", "keyentity:gameprovider:create": "Add game provider", "keyentity:gameprovider:change-state": "Change status of game provider", "keyentity:gameprovider:change-secret": "Change game provider secret", "keyentity:gamelabel": "Manage game labels", "keyentity:gamelabel:view": "View game labels", "keyentity:gamecategory": "Manage game categories", "keyentity:gamecategory:view": "View game categories", "keyentity:gamecategory:create": "Create game categories", "keyentity:gamecategory:edit": "Update game categories", "keyentity:gamecategory:delete": "Delete game categories", "keyentity:gamecategory:change-ordering": "Change order of game category", "keyentity:gameprovider:game": "Manage games of gameprovider", "keyentity:gameprovider:game:view": "View games of gameprovider", "keyentity:gameprovider:game:create": "Create game for gameprovider", "keyentity:gameprovider:game:edit": "Update game for gameprovider", "keyentity:gameprovider:game:delete": "Delete game of gameprovider", "keyentity:integration": "Manage integrations", "keyentity:integration:view": "View integrations", "keyentity:integration:create": "Create integrations", "keyentity:integration:edit": "Edit integrations", "keyentity:integration:delete": "Delete integrations", "keyentity:lobby": "Manage lobbies", "keyentity:lobby:view": "View lobbies", "keyentity:lobby:create": "Create lobbies", "keyentity:lobby:edit": "Update lobbies", "keyentity:lobby:delete": "Delete lobbies", "keyentity:terminal": "Manage terminals", "keyentity:terminal:create": "Create terminals", "keyentity:terminal:delete": "Delete terminals", "keyentity:terminal:edit": "Update terminals", "keyentity:terminal:token": "Manage lobby-wrapper tokens", "keyentity:terminal:view": "View terminals", "keyentity:jurisdiction": "Manage keyEntity jurisdictions", "keyentity:jurisdiction:view": "View keyEntity jurisdictions", "keyentity:jurisdiction:create": "Create keyEntity jurisdictions", "keyentity:jurisdiction:edit": "Update keyEntity jurisdictions", "keyentity:jurisdiction:delete": "Delete keyEntity jurisdictions", "keyentity:merchant": "Manage merchants of key entity", "keyentity:bulk-operation": "Use entity bulk operations", "keyentity:merchant:create": "Create merchants of key entity", "keyentity:merchant:view": "View merchants of key entity", "keyentity:merchant:edit": "Edit merchants of key entity", "keyentity:report": "Get keyEntity brand report", "keyentity:report:currency": "Get keyEntity brand currency report", "keyentity:report:wallet-currency": "Get Entity brand currency report based on wallet data", "keyentity:report:players": "Get keyEntity players report", "keyentity:report:games": "Get keyEntity games daily report", "keyentity:report:ggr": "Get keyEntity ggr total report", "keyentity:report:promo": "Get keyEntity brand promotion report", "keyentity:report:jackpot": "Get keyEntity brand jackpot report", "keyentity:report:jackpot:contributions": "Get keyEntity contributions to jackpots", "keyentity:report:jackpot:contributions:players": "Get keyEntity player's contribution to jackpots", "keyentity:report:jackpot:contributions:logs": "Get keyEntity jackpot contribution logs", "keyentity:report:jackpot:contributions:wins": "Get keyEntity jackpot wins logs", "keyentity:role": "Manage roles of key entity", "keyentity:role:create": "Create role of key entity", "keyentity:role:edit": "Edit role details of key entity", "keyentity:role:delete": "Delete role of key entity", "keyentity:role:view": "View role of key entity", "keyentity:role:move": "Relocate role of key entity", "keyentity:audit": "View audit log", "keyentity:agent": "Manage agents", "keyentity:agent:view": "View agents", "keyentity:site": "Manage site access of entity", "keyentity:payment": "Payment methods management", "keyentity:payment:view": "View payment methods of entity", "keyentity:payment:edit": "Edit payment method of entity", "keyentity:payment:create": "Add payment methods of entity", "keyentity:payment:execute": "Start/init payment on key entity", "keyentity:payment:add-key": "Add public key to gateway", "keyentity:payment:get-key": "Get public key from gateway", "keyentity:payment:transfer-in": "Transfering money to player", "keyentity:payment:transfer-out": "Transfering money from player", "keyentity:notifications": "Manage notifications", "keyentity:notifications:view": "View notifications of key entity", "keyentity:promotion": "Manage keyentity promotions", "keyentity:promotion:skywind": "Manage keyentity skywind promotions", "keyentity:promotion:skywind:create": "Create keyentity skywind promotions", "keyentity:promotion:skywind:edit": "Update keyentity skywind promotions", "keyentity:promotion:owner": "Update owner of keyentity promotion", "keyentity:promotion:view": "View promotions", "keyentity:promotion:create": "Create keyentity promotions", "keyentity:promotion:edit": "Update keyentity promotions", "keyentity:promotion:delete": "Delete keyentity promotions", "keyentity:promotion:bonuscoin": "Manage keyentity bonus coin promo", "keyentity:promotion:freebet": "Manage keyentity freebet promo", "keyentity:promotion:bonuscoin:view": "View keyentity bonus coin promo", "keyentity:promotion:freebet:view": "View keyentity freebet promo", "keyentity:promotion:bonuscoin:create": "Create keyentity bonus coin promotions", "keyentity:promotion:freebet:create": "Create keyentity freebet promotions", "keyentity:promotion:bonuscoin:edit": "Update keyentity bonus coin promotions", "keyentity:promotion:freebet:edit": "Update keyentity freebet promotions", "keyentity:promotion:bonuscoin:delete": "Delete keyentity bonus coin promotions", "keyentity:promotion:freebet:delete": "Delete keyentity freebet promotions", "keyentity:favoritegames": "Gets list of favorite games", "keyentity:recentlygames": "Gets list of recently played games", "promotion": "Manage promotions", "promotion:view": "View promotions", "promotion:create": "Create promotions", "promotion:edit": "Update promotions", "promotion:delete": "Delete promotions", "promotion:skywind": "Manage skywind promotions", "promotion:skywind:create": "Create skywind promotions", "promotion:skywind:edit": "Update skywind promotions", "promotion:owner": "Update owner of promotion", "promotion:bonuscoin": "Manage bonus coin promo", "promotion:freebet": "Manage freebet promo", "promotion:bonuscoin:view": "View bonus coin promo", "promotion:freebet:view": "View freebet promo", "promotion:bonuscoin:create": "Create bonus coin promotions", "promotion:freebet:create": "Create freebet promotions", "promotion:bonuscoin:edit": "Update bonus coin promotions", "promotion:freebet:edit": "Update freebet promotions", "promotion:bonuscoin:delete": "Delete bonus coin promotions", "promotion:freebet:delete": "Delete freebet promotions", "keyentity:playersession:find": "Find player session", "keyentity:playersession:kill": "Kill player session", "keyentity:cashier": "Access to Cashier page", "keyentity:entitydomain": "Manage entitydomains", "keyentity:entitydomain:static": "Manage static entitydomains", "keyentity:entitydomain:static:view": "View static entitydomains", "keyentity:entitydomain:static:remove": "Remove static entitydomains", "keyentity:entitydomain:static:create": "Create static entitydomains", "keyentity:entitydomain:static:edit": "Edit static entitydomains", "keyentity:entitydomain:dynamic": "Manage dynamic entitydomains", "keyentity:entitydomain:dynamic:view": "View dynamic entitydomains", "keyentity:entitydomain:dynamic:remove": "Remove dynamic entitydomains", "keyentity:entitydomain:dynamic:create": "Create dynamic entitydomains", "keyentity:entitydomain:dynamic:edit": "Edit dynamic entitydomains", "keyentity:responsiblegaming:player": "Manage player's responsible gaming settings", "keyentity:responsiblegaming:player:view": "View player's responsible gaming settings", "keyentity:responsiblegaming:player:edit": "Update player's responsible gaming settings", "keyentity:responsiblegaming:player:delete": "Delete player's responsible gaming pending settings changes", "keyentity:gameclose:forcefinish": "Allows to force finish round", "keyentity:external-game-provider:gameclose:forcefinish": "Allows to force finish round for external provider", "entity:external-game-provider:gameclose:forcefinish": "Allows to force finish round for external provider", "keyentity:gameclose:revert": "Allows to revert round", "keyentity:gameclose:retry": "Allows to retry pending operation for round", "keyentity:gameclose:transfer-out": "Allows to transfer-out for round", "keyentity:gameclose:finalize": "Allows to finalize round", "keyentity:external-game-provider:history": "Get external (game provider) history report", "keyentity:external-game-provider:availability": "Get external game providers that are currently in use by key entity", "merchant": "Manage merchants", "merchant:view": "View merchants", "merchant:edit": "Edit merchants", "merchant:create": "Create merchants", "merchanttypes": "Merchant types management", "merchanttypes:view": "Merchant types view", "merchanttypes:create": "Create merchant type", "merchanttypes:edit": "Update merchant type", "schemadefinition": "Schema definition management", "schemadefinition:view": "Schema definition view", "schemadefinition:create": "Create schema definition", "schemadefinition:edit": "Edit schema definition", "schemadefinition:remove": "Remove schema definition", "schemaconfiguration": "Schema configuration management", "schemaconfiguration:view": "Schema configuration view", "schemaconfiguration:create": "Create schema configuration", "schemaconfiguration:edit": "Edit schema configuration", "schemaconfiguration:remove": "Remove schema configuration", "limittemplate": "Limit template management", "limittemplate:view": "Limit template view", "limittemplate:create": "Create limit template", "limittemplate:edit": "Edit limit template", "limittemplate:remove": "Remove limit template", "keyentity:gamelimits": "Game Limits Management for keyentity", "keyentity:gamelimits:view": "Game Limits view for keyentity", "keyentity:gamelimits:create": "Create Game Limits for keyentity", "keyentity:gamelimits:edit": "Edit Game Limits for keyentity", "keyentity:gamelimits:remove": "Remove Game Limits for keyentity", "keyentity:integrationtests": "Manage integration tests for keyentity", "keyentity:integrationtests:view": "View reports on the results of completed integration tests for keyentity", "keyentity:integrationtests:run": "The ability to run integration tests for keyentity", "gamelimits": "Game Limits Management", "gamelimits:view": "Game Limits view", "gamelimits:create": "Create Game Limits", "gamelimits:edit": "Edit Game Limits", "gamelimits:remove": "Remove Game Limits", "settings": "Manage global settings", "srt": "Manage SRT challenges and tournaments", "srt:challenge": "Manage all challenge feature", "srt:tournament": "Manage all tournament feature", "gs:settings": "Manage GS settings", "gs:settings:view": "View GS settings", "gs:settings:edit": "Edit GS settings", "gs:settings:create": "Create GS settings", "gs:settings:remove": "Remove GS settings", "sw:integration:external": "Manage external integrations", "proxy": "Manage proxies", "proxy:view": "View proxies", "proxy:edit": "Edit proxies", "proxy:create": "Create proxies", "proxy:delete": "Delete proxies", "gamegroup": "Manage game groups", "gamegroup:view": "View game groups", "gamegroup:create": "Create gamegroups, manage limits", "gamegroup:edit": "Update gamegroups, manage limits", "gamegroup:delete": "Remove gamegroups, manage limits", "gamegroup:rename": "Rename gamegroup", "email": "Permission for email sending", "deployment": "Permission for deployment group management", "hub:casino": "Permission for casino in the hub", "hub:analytics": "Permission for analytics in the hub", "hub:engagement": "Permission for engagement in the hub", "lobby": "Manage lobbies", "lobby:view": "View lobbies", "lobby:create": "Create lobbies", "lobby:edit": "Update lobbies", "lobby:delete": "Delete lobbies", "merchant:player:gamegroup": "Manage merchant player game groups", "terminal:token:add": "Adds terminal token to blacklist", "terminal:token:delete": "Remove terminal token from blacklist", "keyentity:zone": "Manage zones for keyentity", "keyentity:zone:view": "View zones for keyentity", "keyentity:zone:edit": "Edit zones for keyentity", "keyentity:zone:create": "Create zones for keyentity", "keyentity:zone:delete": "Delete zones for keyentity", "zone": "Manage zones", "zone:view": "View zones", "zone:edit": "Edit zones", "zone:create": "Create zones", "zone:delete": "Delete zones", "keyentity:tag": "Manage tags for keyentity", "keyentity:tag:view": "View tags for keyentity", "keyentity:tag:edit": "Edit tags for keyentity", "keyentity:tag:create": "Create tags for keyentity", "keyentity:tag:delete": "Delete tags for keyentity", "physicaltable": "Manage physical tables", "physicaltable:view": "View physical tables", "physicaltable:edit": "Edit physical tables", "physicaltable:create": "Create physical tables", "physicaltable:delete": "Delete physical tables", "keyentity:physicaltable": "Manage physical tables for keyentity", "keyentity:physicaltable:view": "View physical tables for keyentity", "keyentity:physicaltable:edit": "Edit physical tables for keyentity", "keyentity:physicaltable:create": "Create physical tables for keyentity", "keyentity:physicaltable:delete": "Delete physical tables for keyentity", "dashboard": "Manage dashboards", "dashboard:view": "View dashboards", "dashboard:edit": "Edit dashboards", "dashboard:create": "Create dashboards", "dashboard:delete": "Delete dashboard", "keyentity:dashboard": "Manage dashboards for keyentity", "keyentity:dashboard:view": "View dashboards for keyentity", "keyentity:dashboard:edit": "Edit dashboards for keyentity", "keyentity:dashboard:create": "Create dashboards for keyentity", "keyentity:dashboard:delete": "Delete dashboard for keyentity", "stake-range": "Ability to view stake ranges per currency", "stake-range-update": "Ability to update stake ranges per currency", "stake-range-create": "Ability to create stake ranges per currency", "stake-range-delete": "Ability to drop stake ranges per currency", "hub:studio": "Permission for studio in the hub", "critical-files": "Permission to Critical Files API", "keyentity:provider-game-codes": "Manage provider game codes for keyentity", "keyentity:provider-game-codes:view": "View provider game codes for keyentity", "keyentity:provider-game-codes:edit": "Edit provider game codes for keyentity", "keyentity:provider-game-codes:create": "Create provider game codes for keyentity", "keyentity:provider-game-codes:delete": "Delete provider game codes for keyentity", "entitylabels": "Entity labels management", "entitylabels:view": "Entity labels view", "entitylabels:create": "Entity labels create", "keyentity:entitylabels": "Entity labels management", "keyentity:entitylabels:view": "Entity labels view", "keyentity:entitylabels:create": "Entity labels view", "promolabels": "Promo labels management", "promolabels:view": "Promo labels view", "promolabels:create": "Promo labels create", "keyentity:promolabels": "Promo labels management", "keyentity:promolabels:view": "Promo labels view", "keyentity:promolabels:create": "Promo labels view", "gamelabels": "Game labels management", "gamelabels:view": "Game labels view", "gamelabels:create": "Game labels create", "keyentity:label-groups": "Label groups management", "keyentity:label-groups:view": "Label groups view ", "keyentity:label-groups:create": "Label groups create", "keyentity:limit-level": "Limit level management for keyentity", "keyentity:limit-level:view": "View limit levels for keyentity", "keyentity:limit-level:create": "Create limit levels for keyentity", "keyentity:limit-level:delete": "Delete limit levels by ID for keyentity", "keyentity:limit-level:update": "Update limit levels by ID for keyentity", "limit-level": "Limit level management", "limit-level:view": "View limit levels", "limit-level:create": "Create limit levels", "limit-level:delete": "Delete limit levels by ID", "limit-level:update": "Update limit levels by ID", "keyentity:entity-game-limit-level": "Entity game limit level management", "keyentity:entity-game-limit-level:view": "Entity game limit levels view", "keyentity:entity-game-limit-level:create": "Entity game limit level create", "keyentity:entity-game-limit-level:delete": "Entity game limit level delete", "entity-game-limit-level": "Game limit level management", "entity-game-limit-level:view": "Game limit level view", "entity-game-limit-level:create": "Entity game limit level create", "entity-game-limit-level:delete": "Entity game limit level delete", "business-structure:admin": "Entity info for admin in business structure", "keyentity:flat-reports": "Flat Reports for keyentity", "keyentity:flat-reports:view": "Flat Reports view for keyentity", "flat-reports": "Flat Reports", "flat-reports:view": "Flat Reports view", "entity:live-game": "Manage live game", "entity:live-game:add-live-game": "Add live game", "entity:live-game:remove-live-game": "Remove live game", "entity:live-game:change-state": "Enable disable live game for entity", "entity:live-game:change-state:disabled": "Disable live game for entity", "entity:live-game:change-state:enabled": "Enable live game for entity", "keyentity:live-game": "Manage live game", "keyentity:live-game:change-state": "Change status of live game", "keyentity:live-game:change-state:disabled": "Change status of live game to disabled", "keyentity:live-game:change-state:enabled": "Change status of live game to enabled", "game-limits-currencies": "Ability to view game limits currencies", "game-limits-currencies-update": "Ability to update game limits currency", "game-limits-currencies-create": "Ability to create game limits currency", "game-limits-currencies-delete": "Ability to drop game limits currency", "restricted-countries-solution": "Enable disable ability to use countries from jurisdiction for entity", "keyentity:jp-config-report": "View jackpots configuration for key entity", "jp-config-report": "View jackpots configuration", "keyentity:view:game-contexts": "View unfinished game-contexts", "report:jackpot:instances": "View info about all jackpot instances that belong to brand's/merchant's games", "keyentity:report:jackpot:instances": "View info about all jackpot instances that belong to keyentity's games", "player:reset-change-nickname-attempts": "Reset nickname change attempts", "player:change-nickname": "Change nickname", "keyentity:player:change-nickname": "Change nickname"}}}, "paths": {"$allOf": [{"$ref": "./mapi-swagger/gameserver.json"}, {"$ref": "./mapi-swagger/entity.json"}, {"$ref": "./mapi-swagger/country.json"}, {"$ref": "./mapi-swagger/jurisdiction.json"}, {"$ref": "./mapi-swagger/currency.json"}, {"$ref": "./mapi-swagger/language.json"}, {"$ref": "./mapi-swagger/finance.json"}, {"$ref": "./mapi-swagger/player.json"}, {"$ref": "./mapi-swagger/payments.json"}, {"$ref": "./mapi-swagger/user.json"}, {"$ref": "./mapi-swagger/game-group.json"}, {"$ref": "./mapi-swagger/label.json"}, {"$ref": "./mapi-swagger/game-category.json"}, {"$ref": "./mapi-swagger/game.json"}, {"$ref": "./mapi-swagger/reports.json"}, {"$ref": "./mapi-swagger/site-security.json"}, {"$ref": "./mapi-swagger/permissions.json"}, {"$ref": "./mapi-swagger/phantom.json"}, {"$ref": "./mapi-swagger/game-provider.json"}, {"$ref": "./mapi-swagger/settings.json"}, {"$ref": "./mapi-swagger/merchant.json"}, {"$ref": "./mapi-swagger/downloadable-lobby.json"}, {"$ref": "./mapi-swagger/lobby.json"}, {"$ref": "./mapi-swagger/terminal.json"}, {"$ref": "./mapi-swagger/history.json"}, {"$ref": "./mapi-swagger/history-promo.json"}, {"$ref": "./mapi-swagger/agent.json"}, {"$ref": "./mapi-swagger/available-sites.json"}, {"$ref": "./mapi-swagger/notifications.json"}, {"$ref": "./mapi-swagger/reports-jackpot.json"}, {"$ref": "./mapi-swagger/promo.json"}, {"$ref": "./mapi-swagger/blocked-players.json"}, {"$ref": "./mapi-swagger/domain.json"}, {"$ref": "./mapi-swagger/static-domain-pool.json"}, {"$ref": "./mapi-swagger/dynamic-domain-pool.json"}, {"$ref": "./mapi-swagger/responsible-gaming.json"}, {"$ref": "./mapi-swagger/health.json"}, {"$ref": "./mapi-swagger/business-intelligence.json"}, {"$ref": "./mapi-swagger/version.json"}, {"$ref": "./mapi-swagger/id.json"}, {"$ref": "./mapi-swagger/proxy.json"}, {"$ref": "./mapi-swagger/email.json"}, {"$ref": "./mapi-swagger/merchant-type.json"}, {"$ref": "./mapi-swagger/schema-definition.json"}, {"$ref": "./mapi-swagger/schema-configuration.json"}, {"$ref": "./mapi-swagger/limit-template.json"}, {"$ref": "./mapi-swagger/game-limits.json"}, {"$ref": "./mapi-swagger/deployment-group.json"}, {"$ref": "./mapi-swagger/rtpHistory.json"}, {"$ref": "./mapi-swagger/merchant-player-gamegroup.json"}, {"$ref": "./mapi-swagger/stakeRange.json"}, {"$ref": "./mapi-swagger/integration-tests.json"}, {"$ref": "./mapi-swagger/limit-level.json"}, {"$ref": "./mapi-swagger/test-players.json"}, {"$ref": "./mapi-swagger/gitbook.json"}, {"$ref": "./mapi-swagger/flat-reports.json"}, {"$ref": "./mapi-swagger/game-limits-currencies.json"}, {"$ref": "./mapi-swagger/entity-jackpots-config.json"}, {"$ref": "./mapi-swagger/jackpot.json"}, {"$ref": "./mapi-swagger/oAuth.json"}]}, "definitions": {"$ref": "./mapi-swagger/definitions.json"}, "parameters": {"$ref": "./mapi-swagger/parameters.json"}}