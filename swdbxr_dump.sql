--
-- PostgreSQL database dump
--

-- Dumped from database version 10.23 (Ubuntu 10.23-1.pgdg18.04+1)
-- Dumped by pg_dump version 10.23 (Ubuntu 10.23-1.pgdg18.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: swsystem; Type: SCHEMA; Schema: -; Owner: swsystem
--

CREATE SCHEMA swsystem;


ALTER SCHEMA swsystem OWNER TO swsystem;

--
-- Name: swxrates; Type: SCHEMA; Schema: -; Owner: swsystem
--

CREATE SCHEMA swxrates;


ALTER SCHEMA swxrates OWNER TO swsystem;

--
-- Name: plpgsql; Type: EXTENSION; Schema: -; Owner: 
--

CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION plpgsql; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION plpgsql IS 'PL/pgSQL procedural language';


--
-- Name: enum_exchange_rate_type; Type: TYPE; Schema: swxrates; Owner: swsystem
--

CREATE TYPE swxrates.enum_exchange_rate_type AS ENUM (
    'bid',
    'ask'
);


ALTER TYPE swxrates.enum_exchange_rate_type OWNER TO swsystem;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: databasechangelog; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.databasechangelog (
    id character varying(255) NOT NULL,
    author character varying(255) NOT NULL,
    filename character varying(255) NOT NULL,
    dateexecuted timestamp without time zone NOT NULL,
    orderexecuted integer NOT NULL,
    exectype character varying(10) NOT NULL,
    md5sum character varying(35),
    description character varying(255),
    comments character varying(255),
    tag character varying(255),
    liquibase character varying(20),
    contexts character varying(255),
    labels character varying(255),
    deployment_id character varying(10)
);


ALTER TABLE swsystem.databasechangelog OWNER TO swsystem;

--
-- Name: databasechangeloglock; Type: TABLE; Schema: swsystem; Owner: swsystem
--

CREATE TABLE swsystem.databasechangeloglock (
    id integer NOT NULL,
    locked boolean NOT NULL,
    lockgranted timestamp without time zone,
    lockedby character varying(255)
);


ALTER TABLE swsystem.databasechangeloglock OWNER TO swsystem;

--
-- Name: exchange_rates; Type: TABLE; Schema: swxrates; Owner: swsystem
--

CREATE TABLE swxrates.exchange_rates (
    rate_date date NOT NULL,
    from_currency character(3) NOT NULL,
    to_currency character(3) NOT NULL,
    rate numeric NOT NULL,
    provider_date date NOT NULL,
    provider character varying(255),
    created_at timestamp without time zone NOT NULL,
    type swxrates.enum_exchange_rate_type DEFAULT 'bid'::swxrates.enum_exchange_rate_type NOT NULL
);


ALTER TABLE swxrates.exchange_rates OWNER TO swsystem;

--
-- Name: TABLE exchange_rates; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON TABLE swxrates.exchange_rates IS 'Currency exchange rates';


--
-- Name: COLUMN exchange_rates.rate_date; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.rate_date IS 'Date of when currency rate is used in the system';


--
-- Name: COLUMN exchange_rates.from_currency; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.from_currency IS 'Currency code of rate source';


--
-- Name: COLUMN exchange_rates.to_currency; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.to_currency IS 'Currency code of rate target';


--
-- Name: COLUMN exchange_rates.rate; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.rate IS 'Exchange rate value';


--
-- Name: COLUMN exchange_rates.provider_date; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.provider_date IS 'Date of currency rate provider';


--
-- Name: COLUMN exchange_rates.provider; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.provider IS 'Currency rate provider (oanda, oxr, etc)';


--
-- Name: COLUMN exchange_rates.created_at; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.created_at IS 'Creation time';


--
-- Name: COLUMN exchange_rates.type; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.exchange_rates.type IS 'Exchange rate type - ask or bid';


--
-- Name: game_limits_currencies; Type: TABLE; Schema: swxrates; Owner: swsystem
--

CREATE TABLE swxrates.game_limits_currencies (
    currency character(3) NOT NULL,
    to_eur_multiplier integer,
    copy_limits_from character(3),
    version integer NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE swxrates.game_limits_currencies OWNER TO swsystem;

--
-- Name: TABLE game_limits_currencies; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON TABLE swxrates.game_limits_currencies IS 'Table to keep game limits currencies parameters';


--
-- Name: COLUMN game_limits_currencies.currency; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.game_limits_currencies.currency IS 'Currency code';


--
-- Name: COLUMN game_limits_currencies.to_eur_multiplier; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.game_limits_currencies.to_eur_multiplier IS 'Currency toEURMultiplier';


--
-- Name: COLUMN game_limits_currencies.copy_limits_from; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.game_limits_currencies.copy_limits_from IS 'Alternative currency from which limits can be copied';


--
-- Name: COLUMN game_limits_currencies.version; Type: COMMENT; Schema: swxrates; Owner: swsystem
--

COMMENT ON COLUMN swxrates.game_limits_currencies.version IS 'Version of game limits currency parameters set';


--
-- Name: databasechangeloglock pk_databasechangeloglock; Type: CONSTRAINT; Schema: swsystem; Owner: swsystem
--

ALTER TABLE ONLY swsystem.databasechangeloglock
    ADD CONSTRAINT pk_databasechangeloglock PRIMARY KEY (id);


--
-- Name: exchange_rates exchange_rates_pkey; Type: CONSTRAINT; Schema: swxrates; Owner: swsystem
--

ALTER TABLE ONLY swxrates.exchange_rates
    ADD CONSTRAINT exchange_rates_pkey PRIMARY KEY (rate_date, from_currency, to_currency, type);


--
-- Name: game_limits_currencies game_limits_currencies_pkey; Type: CONSTRAINT; Schema: swxrates; Owner: swsystem
--

ALTER TABLE ONLY swxrates.game_limits_currencies
    ADD CONSTRAINT game_limits_currencies_pkey PRIMARY KEY (currency, version);


--
-- Name: idx_exchange_rates_rate_date_type; Type: INDEX; Schema: swxrates; Owner: swsystem
--

CREATE INDEX idx_exchange_rates_rate_date_type ON swxrates.exchange_rates USING btree (rate_date, type);


--
-- Name: SCHEMA swxrates; Type: ACL; Schema: -; Owner: swsystem
--

GRANT USAGE ON SCHEMA swxrates TO swxrates;


--
-- Name: TABLE exchange_rates; Type: ACL; Schema: swxrates; Owner: swsystem
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swxrates.exchange_rates TO swxrates;


--
-- Name: TABLE game_limits_currencies; Type: ACL; Schema: swxrates; Owner: swsystem
--

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE swxrates.game_limits_currencies TO swxrates;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: swxrates; Owner: swsystem
--

ALTER DEFAULT PRIVILEGES FOR ROLE swsystem IN SCHEMA swxrates GRANT SELECT,USAGE ON SEQUENCES  TO swxrates;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: swxrates; Owner: swsystem
--

ALTER DEFAULT PRIVILEGES FOR ROLE swsystem IN SCHEMA swxrates GRANT SELECT,INSERT,DELETE,UPDATE ON TABLES  TO swxrates;


--
-- PostgreSQL database dump complete
--

